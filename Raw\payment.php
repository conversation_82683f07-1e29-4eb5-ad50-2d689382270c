/**
 * ========================================
 * ASG PAYMENT PAGE - PayPal Integration
 * ========================================
 *
 * Optimized payment page for AbilitySeminarsGroup
 * Complete PayPal SDK integration
 * Automatic post-payment processing
 *
 * URL: /payment/?course=course_1&price=99
 *
 * Version: 1.0.0 - PAYMENT SYSTEM
 * Author: ASG Development Team
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * ========================================
 * PAYMENT PAGE SHORTCODE
 * ========================================
 */
add_shortcode('asg_payment_page', 'asg_payment_page_handler');

function asg_payment_page_handler($atts) {
    // Verify user is logged in
    if (!is_user_logged_in()) {
        return '<div class="asg-error">
            <h3>Access Restricted</h3>
            <p>You must log in to access this page.</p>
            <a href="' . wp_login_url(get_permalink()) . '" class="btn btn-primary">Log In</a>
        </div>';
    }

    // Get URL parameters
    $course_code = sanitize_text_field($_GET['course'] ?? '');
    $price = floatval($_GET['price'] ?? 0);
    $new_user = isset($_GET['new_user']) ? '1' : '0';

    if (empty($course_code) || $price <= 0) {
        return '<div class="asg-error">
            <h3>Parameter Error</h3>
            <p>Incomplete course information. Please return to the course page.</p>
            <a href="/" class="btn btn-secondary">Back to Home</a>
        </div>';
    }

    // Get course information
    global $wpdb;
    $course = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM wpic_courses WHERE code_course = %s AND status_course = 'published'",
        $course_code
    ));

    if (!$course) {
        return '<div class="asg-error">
            <h3>Course Not Found</h3>
            <p>The requested course is not available.</p>
            <a href="/" class="btn btn-secondary">Back to Home</a>
        </div>';
    }

    // Check if already enrolled
    $current_user = wp_get_current_user();
    $existing_enrollment = $wpdb->get_row($wpdb->prepare("
        SELECT e.* FROM wpic_asg_enrollments e
        JOIN wpic_asg_students s ON e.student_id = s.id_student
        WHERE s.id_user = %d AND e.course_id = %d
    ", $current_user->ID, $course->id_course));

    if ($existing_enrollment) {
        return '<div class="asg-success">
            <h3>Already Enrolled</h3>
            <p>You already have access to this course.</p>
            <a href="/lessons/?course=' . $course_code . '" class="btn btn-success">Go to Lessons</a>
        </div>';
    }

    // Generate payment page HTML
    ob_start();
    ?>

    <div class="asg-payment-container">
        <!-- Welcome Header -->
        <?php if ($new_user === '1'): ?>
        <div class="welcome-banner">
            <h2>🎉 Welcome, <?php echo esc_html($current_user->display_name); ?>!</h2>
            <p>Your account has been created successfully. Now complete your enrollment:</p>
        </div>
        <?php endif; ?>

        <!-- Two Column Layout -->
        <div class="payment-layout">
            <!-- Left Column - Checkout -->
            <div class="checkout-section">
                <h1 class="checkout-title">Checkout</h1>

                <!-- Billing Address -->
                <div class="billing-section">
                    <h3>Billing address</h3>
                    <div class="form-group">
                        <label for="country">Country</label>
                        <div class="country-selector">
                            <span class="flag">🇩🇴</span>
                            <span>Dominican Republic</span>
                        </div>
                        <p class="billing-note">Ability Seminars is required by law to collect applicable transaction taxes for purchases made in certain tax jurisdictions.</p>
                    </div>
                </div>

                <!-- Payment Method -->
                <div class="payment-method-section">
                    <h3>Payment Method</h3>
                    <div class="paypal-option">
                        <div class="paypal-logo">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTcuMDc2IDIuNTc2SDE2LjkyNEMxOC4wNzYgMi41NzYgMTkuMDc2IDMuNTc2IDE5LjA3NiA0LjcyOFYxOS4yNzJDMTkuMDc2IDIwLjQyNCAxOC4wNzYgMjEuNDI0IDE2LjkyNCAyMS40MjRINy4wNzZDNS45MjQgMjEuNDI0IDQuOTI0IDIwLjQyNCA0LjkyNCAxOS4yNzJWNC43MjhDNC45MjQgMy41NzYgNS45MjQgMi41NzYgNy4wNzYgMi41NzZaIiBmaWxsPSIjMDAzMDg3Ii8+CjxwYXRoIGQ9Ik0xMi4wMDEgMTguNTc2QzE1LjMxNCAxOC41NzYgMTguMDAxIDE1Ljg4OSAxOC4wMDEgMTIuNTc2QzE4LjAwMSA5LjI2MyAxNS4zMTQgNi41NzYgMTIuMDAxIDYuNTc2QzguNjg4IDYuNTc2IDYuMDAxIDkuMjYzIDYuMDAxIDEyLjU3NkM2LjAwMSAxNS44ODkgOC42ODggMTguNTc2IDEyLjAwMSAxOC41NzZaIiBmaWxsPSIjMDA5Q0RBIi8+Cjwvc3ZnPgo=" alt="PayPal" />
                            <span>PayPal</span>
                        </div>
                    </div>
                </div>

                <!-- Order Details -->
                <div class="order-details-section">
                    <h3>Orders details (1 course)</h3>
                    <div class="course-item">
                        <?php if (!empty($course->cover_img)): ?>
                        <img src="<?php echo esc_url($course->cover_img); ?>" alt="<?php echo esc_attr($course->name_course); ?>" class="course-thumbnail">
                        <?php endif; ?>
                        <div class="course-info">
                            <h4><?php echo esc_html($course->name_course); ?></h4>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column - Order Summary -->
            <div class="order-summary-section">
                <h2 class="summary-title">Order summary</h2>

                <div class="price-details">
                    <div class="price-row">
                        <span>Original Price:</span>
                        <span>$<?php echo number_format($price, 2); ?></span>
                    </div>
                    <div class="price-row total-row">
                        <span>Total (1 course)</span>
                        <span>$<?php echo number_format($price, 2); ?></span>
                    </div>
                </div>

                <p class="terms-text">By completing your purchase, you agree to these <a href="#">Terms of Use</a>.</p>

                <!-- PayPal Button Container -->
                <div id="paypal-button-container" class="payment-button"></div>

                <!-- Money Back Guarantee -->
                <div class="guarantee-info">
                    <h4>30-Day Money-Back Guarantee</h4>
                    <p>Not satisfied? Get a full refund within 30 days.<br>Simple and straightforward!</p>
                </div>

                <!-- Loading State -->
                <div id="payment-loading" style="display: none;">
                    <div class="loading-spinner"></div>
                    <p>Processing payment...</p>
                </div>

                <!-- Success State -->
                <div id="payment-success" style="display: none;">
                    <div class="success-message">
                        <h3>🎉 Payment Successful!</h3>
                        <p>Your enrollment has been processed. Redirecting to lessons...</p>
                    </div>
                </div>

                <!-- Error State -->
                <div id="payment-error" style="display: none;">
                    <div class="error-message">
                        <h3>❌ Payment Error</h3>
                        <p id="error-details">There was a problem processing your payment. Please try again.</p>
                        <button onclick="location.reload()" class="btn btn-primary">Retry</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- CSS Styles -->
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Outfit:wght@300;400;500;600;700&display=swap');

        .asg-payment-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            font-family: 'Outfit', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
         
            min-height: 100vh;
        }

        .welcome-banner {
            background: linear-gradient(135deg, #0C1B41 0%, #2E5F8A 50%, #4A90E2 100%);
            color: white;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(12, 27, 65, 0.15);
        }

        .welcome-banner h2 {
            margin: 0 0 10px 0;
            font-weight: 600;
        }

        .payment-layout {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 40px;
            align-items: start;
        }

        .checkout-section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .checkout-title {
            font-size: 28px;
            font-weight: 600;
            color: #0C1B41;
            margin: 0 0 30px 0;
        }

        .billing-section, .payment-method-section, .order-details-section {
            margin-bottom: 30px;
        }

        .billing-section h3, .payment-method-section h3, .order-details-section h3 {
            font-size: 18px;
            font-weight: 600;
            color: #0C1B41;
            margin: 0 0 15px 0;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            font-weight: 500;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .country-selector {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            background: white;
            cursor: pointer;
            transition: border-color 0.3s ease;
        }

        .country-selector:hover {
            border-color: #4A90E2;
        }

        .flag {
            font-size: 18px;
        }

        .billing-note {
            font-size: 13px;
            color: #6c757d;
            margin-top: 10px;
            line-height: 1.4;
        }

        .paypal-option {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            background: white;
            transition: border-color 0.3s ease;
        }

        .paypal-option:hover {
            border-color: #4A90E2;
        }

        .paypal-logo {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .paypal-logo img {
            width: 24px;
            height: 24px;
        }

        .paypal-logo span {
            font-weight: 600;
            color: #0C1B41;
        }

        .course-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: #f8f9fa;
        }

        .course-thumbnail {
            width: 80px;
            height: 60px;
            object-fit: cover;
            border-radius: 6px;
            flex-shrink: 0;
        }

        .course-info h4 {
            margin: 0;
            font-size: 16px;
            font-weight: 500;
            color: #0C1B41;
        }

        .order-summary-section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            position: sticky;
            top: 20px;
        }

        .summary-title {
            font-size: 24px;
            font-weight: 600;
            color: #0C1B41;
            margin: 0 0 25px 0;
        }

        .price-details {
            margin-bottom: 20px;
        }

        .price-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .price-row:last-child {
            border-bottom: none;
        }

        .total-row {
            font-weight: 600;
            font-size: 18px;
            color: #0C1B41;
            border-top: 2px solid #e9ecef;
            padding-top: 15px;
            margin-top: 10px;
        }

        .terms-text {
            font-size: 13px;
            color: #6c757d;
            text-align: center;
            margin: 20px 0;
            line-height: 1.4;
        }

        .terms-text a {
            color: #4A90E2;
            text-decoration: none;
        }

        .terms-text a:hover {
            text-decoration: underline;
        }

        .payment-button {
            margin: 25px 0;
            min-height: 50px;
        }

        .guarantee-info {
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #28a745;
            margin-top: 25px;
        }

        .guarantee-info h4 {
            margin: 0 0 10px 0;
            font-size: 16px;
            font-weight: 600;
            color: #0C1B41;
        }

        .guarantee-info p {
            margin: 0;
            font-size: 14px;
            color: #495057;
            line-height: 1.4;
        }

        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4A90E2;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .success-message, .error-message {
            text-align: center;
            padding: 25px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .success-message {
            background: linear-gradient(135deg, #d4edda 0%, #e8f5e8 100%);
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .error-message {
            background: linear-gradient(135deg, #f8d7da 0%, #fce4e6 100%);
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .btn {
            display: inline-block;
            padding: 14px 28px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            text-align: center;
            cursor: pointer;
            border: none;
            transition: all 0.3s ease;
            font-family: 'Outfit', sans-serif;
        }

        .btn-primary {
            background: linear-gradient(135deg, #0C1B41 0%, #2E5F8A 100%);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #2E5F8A 0%, #4A90E2 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(12, 27, 65, 0.25);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
        }

        .asg-error, .asg-success {
            text-align: center;
            padding: 40px;
            border-radius: 12px;
            margin: 20px 0;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .asg-error {
            background: linear-gradient(135deg, #f8d7da 0%, #fce4e6 100%);
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .asg-success {
            background: linear-gradient(135deg, #d4edda 0%, #e8f5e8 100%);
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        @media (max-width: 968px) {
            .payment-layout {
                grid-template-columns: 1fr;
                gap: 30px;
            }

            .order-summary-section {
                position: static;
            }
        }

        @media (max-width: 768px) {
            .asg-payment-container {
                padding: 15px;
            }

            .checkout-section, .order-summary-section {
                padding: 20px;
            }

            .checkout-title {
                font-size: 24px;
            }

            .summary-title {
                font-size: 20px;
            }

            .course-item {
                flex-direction: column;
                text-align: center;
            }

            .course-thumbnail {
                width: 100%;
                height: 120px;
            }
        }
    </style>

    <!-- PayPal SDK -->
    <?php
    // Simple PayPal configuration
    $paypal_client_id = 'AeD6bhA38IWPGaAC8r72hqmCP0Z_3vsYB0M3yeGxKlwEcGumXrxTLKgu151C-XPSIZ7z9HefJ2fb9rdU'; // Sandbox
    // For production, change to your real Client ID
    ?>
    <script src="https://www.paypal.com/sdk/js?client-id=<?php echo $paypal_client_id; ?>&currency=USD"></script>

    <!-- Payment Integration JavaScript -->
    <script>
        // Course configuration
        const COURSE_CONFIG = {
            code: '<?php echo esc_js($course_code); ?>',
            name: '<?php echo esc_js($course->name_course); ?>',
            price: <?php echo json_encode($price); ?>,
            currency: 'USD'
        };

        // API URLs
        const API_BASE = '<?php echo esc_url(rest_url('asg/v1/')); ?>';
        const NONCE = '<?php echo wp_create_nonce('wp_rest'); ?>';

        // Initialize PayPal when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            initializePayPal();
        });

        function initializePayPal() {
            paypal.Buttons({
                style: {
                    layout: 'vertical',
                    color: 'blue',
                    shape: 'rect',
                    label: 'pay',
                    height: 50,
                    tagline: false
                },

                createOrder: function(data, actions) {
                    return actions.order.create({
                        purchase_units: [{
                            amount: {
                                value: COURSE_CONFIG.price.toString(),
                                currency_code: COURSE_CONFIG.currency
                            },
                            description: `Course: ${COURSE_CONFIG.name}`,
                            custom_id: COURSE_CONFIG.code
                        }]
                    });
                },

                onApprove: function(data, actions) {
                    showLoading();

                    return actions.order.capture().then(function(details) {
                        console.log('PayPal payment captured:', details);

                        // Process enrollment in backend
                        processEnrollment(details);
                    });
                },

                onError: function(err) {
                    console.error('PayPal error:', err);
                    showError('Error processing payment. Please try again.');
                },

                onCancel: function(data) {
                    console.log('PayPal payment cancelled:', data);
                    showError('Payment cancelled. You can try again whenever you want.');
                }

            }).render('#paypal-button-container');
        }

        async function processEnrollment(paypalDetails) {
            try {
                const response = await fetch(API_BASE + 'process-enrollment', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-WP-Nonce': NONCE
                    },
                    body: JSON.stringify({
                        course_code: COURSE_CONFIG.code,
                        paypal_payment_id: paypalDetails.id,
                        amount_paid: COURSE_CONFIG.price
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showSuccess(result.data);

                    // Redirect to lessons after 3 seconds
                    setTimeout(() => {
                        window.location.href = result.data.redirect_url || `/lessons/?course=${COURSE_CONFIG.code}`;
                    }, 3000);
                } else {
                    throw new Error(result.error || 'Error processing enrollment');
                }

            } catch (error) {
                console.error('Enrollment error:', error);
                showError('Error processing enrollment: ' + error.message);
            }
        }

        function showLoading() {
            document.getElementById('paypal-button-container').style.display = 'none';
            document.getElementById('payment-loading').style.display = 'block';
            document.getElementById('payment-success').style.display = 'none';
            document.getElementById('payment-error').style.display = 'none';
        }

        function showSuccess(data) {
            document.getElementById('payment-loading').style.display = 'none';
            document.getElementById('payment-success').style.display = 'block';
            document.getElementById('payment-error').style.display = 'none';

            // Update success message with specific data
            const successDiv = document.getElementById('payment-success');
            successDiv.innerHTML = `
                <div class="success-message">
                    <h3>🎉 Enrollment Successful!</h3>
                    <p>Congratulations, ${data.student_name}! You have successfully enrolled in:</p>
                    <h4>${data.course_name}</h4>
                    <p>${data.lessons_initialized} lessons have been initialized for your progress.</p>
                    <p>Redirecting to lessons in <span id="countdown">3</span> seconds...</p>
                </div>
            `;

            // Visual countdown
            let seconds = 3;
            const countdownEl = document.getElementById('countdown');
            const interval = setInterval(() => {
                seconds--;
                if (countdownEl) countdownEl.textContent = seconds;
                if (seconds <= 0) clearInterval(interval);
            }, 1000);
        }

        function showError(message) {
            document.getElementById('payment-loading').style.display = 'none';
            document.getElementById('payment-success').style.display = 'none';
            document.getElementById('payment-error').style.display = 'block';
            document.getElementById('paypal-button-container').style.display = 'block';

            // Update error message
            document.getElementById('error-details').textContent = message;
        }

        // Function to retry payment
        function retryPayment() {
            document.getElementById('payment-error').style.display = 'none';
            document.getElementById('paypal-button-container').style.display = 'block';
        }

        // Debug: Configuration log
        console.log('ASG Payment Page initialized:', COURSE_CONFIG);
    </script>
    <?php
    return ob_get_clean();
}

/**
 * Register payment page automatically
 */
function asg_register_payment_page() {
    // Check if page already exists
    $page_slug = 'payment';
    $existing_page = get_page_by_path($page_slug);

    if (!$existing_page) {
        // Create page automatically
        $page_data = array(
            'post_title'    => 'Payment',
            'post_content'  => '[asg_payment_page]',
            'post_status'   => 'publish',
            'post_type'     => 'page',
            'post_name'     => $page_slug,
            'post_author'   => 1,
            'comment_status' => 'closed',
            'ping_status'   => 'closed'
        );
        wp_insert_post($page_data);
    }
}

add_action('init', 'asg_register_payment_page');

