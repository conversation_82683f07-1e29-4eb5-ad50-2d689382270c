// Check if user is logged in
$is_user_logged_in = is_user_logged_in();
$user_has_courses = false;
$my_courses_data = [];

// If user is logged in, check if they have courses
if ($is_user_logged_in) {
    // Try internal WordPress REST API call first
    $request = new WP_REST_Request('GET', '/asg/v1/my-courses');
    $response = rest_do_request($request);

    if (!$response->is_error()) {
        $data = $response->get_data();
        error_log('Internal REST API Response: ' . print_r($data, true));

        if ($data && $data['success'] && isset($data['data']['courses']) && !empty($data['data']['courses'])) {
            $user_has_courses = true;
            $my_courses_data = $data['data']['courses'];
        }
    } else {
        error_log('Internal REST API failed, trying external call...');

        // Fallback to external API call with authentication
        $cookies = array();
        foreach ($_COOKIE as $name => $value) {
            if (strpos($name, 'wordpress_') === 0 || strpos($name, 'wp-') === 0) {
                $cookies[] = new WP_Http_Cookie(array(
                    'name' => $name,
                    'value' => $value
                ));
            }
        }

        $my_courses_url = 'https://abilityseminarsgroup.com/wp-json/asg/v1/my-courses';
        $response = wp_remote_get($my_courses_url, array(
            'cookies' => $cookies,
            'headers' => array(
                'X-WP-Nonce' => wp_create_nonce('wp_rest'),
            ),
            'timeout' => 30
        ));

        if (!is_wp_error($response)) {
            $body = wp_remote_retrieve_body($response);
            $data = json_decode($body, true);

            error_log('External API Response: ' . print_r($data, true));

            if ($data && $data['success'] && isset($data['data']['courses']) && !empty($data['data']['courses'])) {
                $user_has_courses = true;
                $my_courses_data = $data['data']['courses'];
            }
        } else {
            error_log('External API Error: ' . print_r($response, true));
        }
    }
}

// Determine if we should show My Learning section
$show_my_learning = $is_user_logged_in && $user_has_courses;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $show_my_learning ? 'My Learning' : 'Courses'; ?> - Ability Seminars Group</title>
    
    <!-- Slick Carousel CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick-theme.css">
    
    <!-- Google Fonts - Outfit -->
    <link href="https://fonts.googleapis.com/css2?family=Outfit:wght@400;700&display=swap" rel="stylesheet">

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Outfit', sans-serif;
          
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
			padding-bottom: 20px;
        }

        /* My Learning Title */
        .my-learning-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #0C1B41;
            margin-bottom: 40px;
            text-align: left;
        }

        /* My Learning Carousel Section */
        .my-learning-carousel {
            margin-bottom: 60px;
            position: relative;
        }

        .carousel-container {
            position: relative;
            overflow: hidden;
        }

        .featured-course-card {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
            margin: 0 10px;
            transition: all 0.3s ease;
            height: 200px;
            display: flex !important;
        }

        .featured-course-card:hover {
            border-color: #0C1B41;
            transform: translateY(-2px);
        }

        .featured-course-image {
            width: 40%;
            height: 100%;
            object-fit: cover;
        }

        .featured-card-content {
            width: 60%;
            padding: 20px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .featured-card-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: #0C1B41;
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .featured-card-description {
            color: #6b7280;
            font-size: 0.9rem;
            line-height: 1.4;
           
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            flex-grow: 1;
        }

        .progress-container {
            margin-top: auto;
        }

        .progress-bar {
            background: #e5e7eb;
            border-radius: 4px;
            height: 6px;
            overflow: hidden;
            margin-bottom: 8px;
        }

        .progress-fill {
            background: #0C1B41;
            height: 100%;
            transition: width 0.3s ease;
        }

        .progress-text {
            font-size: 0.85rem;
            color: #6b7280;
            font-weight: 500;
        }

        /* Courses Section Title */
        .courses-section-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #0C1B41;
            margin-bottom: 30px;
            text-align: left;
        }

        /* Category Filters */
        .filters-section {
            margin-bottom: 40px;
        }

        .category-filters {
            background: linear-gradient(135deg, #0C1B41 0%, #2E5F8A 100%);
            border-radius: 50px;
            padding: 8px;
            display: flex;
            gap: 0;
            overflow-x: auto;
            white-space: nowrap;
        }

        .filter-btn {
            padding: 12px 24px;
            border: none;
            background: transparent;
            color: rgba(255, 255, 255, 0.7);
            border-radius: 50px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            text-transform: capitalize;
            white-space: nowrap;
            flex-shrink: 0;
        }

        .filter-btn:hover,
        .filter-btn.active {
            background: white;
            color: #0C1B41;
        }

        /* Courses Grid */
        .courses-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 20px;
        }

        .course-card {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
            transition: all 0.3s ease;
			
        }

        .course-card:hover {
            border-color: #0C1B41;
            transform: translateY(-2px);
        }

        .course-card img {
            width: 100%;
            height: 50%;
            object-fit: cover;
        }

        .course-card-content {
            padding: 20px;
        }

        .course-title {
            font-size: 1.1rem;
            font-weight: 700;
            color: #0C1B41;
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .course-meta {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            font-size: 0.85rem;
        }

        .rating {
            display: flex;
            align-items: center;
            gap: 2px;
        }

        .rating .fas {
            color: #f59e0b;
            font-size: 0.8rem;
        }

        .course-description {
            color: #6b7280;
            font-size: 0.9rem;
            margin-bottom: 15px;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .course-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 15px;
        }

        .course-price {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .current-price {
            font-size: 1.1rem;
            font-weight: 700;
            color: #0C1B41;
        }

        .original-price {
            text-decoration: line-through;
            color: #9ca3af;
            font-size: 0.9rem;
        }

        .buy-btn {
            background: white;
            color: #0C1B41;
            padding: 8px 20px;
            border: 2px solid #0C1B41;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            font-size: 0.9rem;
        }

        .buy-btn:hover {
            background: #0C1B41;
            color: white;
        }

        /* Slick Carousel Custom Styles */
        .slick-dots {
            display: none !important;
        }

        .slick-next {
            right: -60px;
            width: 50px;
            height: 50px;
            background: #0C1B41 !important;
            border-radius: 50%;
            z-index: 10;
        }

        .slick-prev {
            left: -60px;
            width: 50px;
            height: 50px;
            background: #0C1B41 !important;
            border-radius: 50%;
            z-index: 10;
        }

        .slick-next:before,
        .slick-prev:before {
            color: white;
            font-size: 20px;
            line-height: 1;
        }

        .slick-next:hover,
        .slick-prev:hover {
            background: #2E5F8A !important;
        }

        /* Loading State */
        .loading {
            text-align: center;
            padding: 60px 0;
            color: #64748b;
        }

        .loading i {
            font-size: 2rem;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            body {
                padding: 20px 15px;
            }

            .my-learning-title {
                font-size: 2rem;
                margin-bottom: 30px;
            }

            .courses-section-title {
                font-size: 1.5rem;
                margin-bottom: 20px;
            }

            .featured-course-card {
                flex-direction: column;
                height: auto;
            }

            .featured-course-image {
                width: 100%;
                height: 150px;
            }

            .featured-card-content {
                width: 100%;
            }

            .category-filters {
                padding: 6px;
            }

            .filter-btn {
                padding: 10px 18px;
                font-size: 0.9rem;
            }

            .courses-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .slick-next,
            .slick-prev {
                width: 40px;
                height: 40px;
                right: -50px;
            }

            .slick-prev {
                left: -50px;
            }
        }

        @media (max-width: 480px) {
            .slick-next {
                right: -35px;
            }

            .slick-prev {
                left: -35px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <?php if ($show_my_learning): ?>
        <!-- My Learning Section - Only shown if user is logged in and has courses -->
        <h1 class="my-learning-title">My Courses</h1>



        <div class="my-learning-carousel">
            <div id="featuredCarousel" class="carousel-container">
             
                <?php foreach ($my_courses_data as $index => $course): ?>
                    <?php
                    // Debug: Log each course being processed
                    error_log("Processing course $index: " . print_r($course, true));

                    $progress = isset($course['progress_percentage']) ? $course['progress_percentage'] : 0;
                    $course_name = $course['name_course'] ?? $course['course_name'] ?? '';
                    $course_description = $course['course_description'] ?? '';
                    $course_image = $course['cover_img'] ?? $course['course_image'] ?? '';

                    // Debug: Log what we're about to render
                    error_log("Rendering course: Name=$course_name, Progress=$progress, Image=$course_image");
                    ?>
                    <!-- DEBUG: Course <?php echo $index; ?>: <?php echo esc_html($course_name); ?> -->
                    <div class="featured-course-card">
                        <img src="<?php echo esc_url($course_image); ?>" alt="<?php echo esc_attr($course_name); ?>" class="featured-course-image">
                        <div class="featured-card-content">
                            <div class="featured-card-title"><?php echo esc_html($course_name); ?></div>
                            <div class="featured-card-description"><?php echo esc_html($course_description); ?></div>
                            <div class="progress-container">
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: <?php echo $progress; ?>%;"></div>
                                </div>
                                <div class="progress-text"><?php echo round($progress); ?>% Complete</div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Courses Section -->
        <h2 class="courses-section-title">Courses to get you started</h2>

        <!-- Category Filters -->
        <div class="filters-section">
            <div class="category-filters">
                <button class="filter-btn active" data-category="all">All categories</button>
                <button class="filter-btn" data-category="finance">Finance</button>
                <button class="filter-btn" data-category="marketing">Marketing</button>
                <button class="filter-btn" data-category="personal-development">Personal Development</button>
                <button class="filter-btn" data-category="technology">Technology</button>
                <button class="filter-btn" data-category="business">Business</button>
            </div>
        </div>

        <!-- Courses Grid -->
        <div id="coursesGrid" class="courses-grid">
            <!-- Courses will be loaded here -->
        </div>

        <div id="loading" class="loading" style="display: none;">
            <i class="fas fa-spinner"></i>
            <p>Loading courses...</p>
        </div>
    </div>

    <!-- jQuery and Slick Carousel JS -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"></script>

    <script>
        // Global variables
        let allCourses = [];
        let myCourses = <?php echo json_encode($my_courses_data); ?>;
        let currentFilter = 'all';
        let isUserLoggedIn = <?php echo $is_user_logged_in ? 'true' : 'false'; ?>;
        let userHasCourses = <?php echo $user_has_courses ? 'true' : 'false'; ?>;

        // Debug PHP data
        console.log('=== PHP DATA DEBUG ===');
        console.log('Is user logged in (PHP):', <?php echo json_encode($is_user_logged_in); ?>);
        console.log('User has courses (PHP):', <?php echo json_encode($user_has_courses); ?>);
        console.log('Show my learning (PHP):', <?php echo json_encode($show_my_learning); ?>);
        console.log('My courses data (PHP):', <?php echo json_encode($my_courses_data); ?>);
        console.log('=== END PHP DATA DEBUG ===');

        // Initialize the page
        $(document).ready(function() {
            console.log('Page loaded, starting initialization...');
            console.log('User logged in:', isUserLoggedIn);
            console.log('User has courses:', userHasCourses);
            console.log('My courses from PHP:', myCourses);

            loadData();
            setupEventListeners();

            // Initialize carousel if user has courses
            <?php if ($show_my_learning): ?>
            initializeCarousel();
            <?php endif; ?>
        });

        // Load data from APIs
        async function loadData() {
            showLoading(true);

            try {
                // Load all courses
                const allCoursesResponse = await fetch('https://abilityseminarsgroup.com/wp-json/asg/v1/courses/api');

                if (!allCoursesResponse.ok) {
                    throw new Error(`HTTP error! status: ${allCoursesResponse.status}`);
                }

                const allCoursesData = await allCoursesResponse.json();
                console.log('=== ALL COURSES API RESPONSE ===');
                console.log('Full API Response:', allCoursesData);

                if (allCoursesData.success && allCoursesData.data) {
                    allCourses = allCoursesData.data;
                    console.log('Loaded courses count:', allCourses.length);
                    console.log('First course sample:', allCourses[0]);
                    console.log('All courses data:', allCourses);
                } else {
                    console.error('API returned success=false or no data');
                    console.error('API response structure:', allCoursesData);
                    allCourses = [];
                }
                console.log('=== END ALL COURSES API RESPONSE ===');

                // Render the courses grid
                renderCoursesGrid();

            } catch (error) {
                console.error('Error loading data:', error);
                $('#coursesGrid').html('<p style="text-align: center; color: #ef4444;">Error loading courses. Please try again later.</p>');
            } finally {
                showLoading(false);
            }
        }

        // Initialize carousel for My Learning section
        function initializeCarousel() {
            const carouselCards = $('#featuredCarousel .featured-course-card');
            console.log('Initializing carousel with', carouselCards.length, 'cards');

            if (carouselCards.length > 0) {
                console.log('Carousel cards found, initializing Slick...');
                $('#featuredCarousel').slick({
                    infinite: false,
                    slidesToShow: Math.min(3, carouselCards.length),
                    slidesToScroll: 1,
                    autoplay: false,
                    arrows: carouselCards.length > 3,
                    dots: false,
                    prevArrow: '<button type="button" class="slick-prev"><i class="fas fa-chevron-left"></i></button>',
                    nextArrow: '<button type="button" class="slick-next"><i class="fas fa-chevron-right"></i></button>',
                    responsive: [
                        {
                            breakpoint: 768,
                            settings: {
                                slidesToShow: 1,
                                arrows: carouselCards.length > 1
                            }
                        },
                        {
                            breakpoint: 1024,
                            settings: {
                                slidesToShow: 2,
                                arrows: carouselCards.length > 2
                            }
                        }
                    ]
                });
                console.log('Carousel initialized successfully');
            } else {
                console.log('No carousel cards found');
            }
        }



        // Render courses grid
        function renderCoursesGrid() {
            console.log('Rendering courses grid...');
            console.log('All courses:', allCourses);
            console.log('Current filter:', currentFilter);

            if (!allCourses || !Array.isArray(allCourses) || allCourses.length === 0) {
                console.log('No courses available');
                $('#coursesGrid').html('<p style="text-align: center; color: #64748b; grid-column: 1 / -1; padding: 40px;">No courses available.</p>');
                return;
            }

            const filteredCourses = currentFilter === 'all'
                ? allCourses.slice().reverse()
                : allCourses.filter(course => course.category_course === currentFilter);

            console.log('Filtered courses:', filteredCourses.length);

            let html = '';

            filteredCourses.forEach((course, index) => {
                console.log(`Processing course ${index + 1}:`, course);

                // Check if user is enrolled in this course
                const isEnrolled = myCourses && Array.isArray(myCourses) &&
                    myCourses.some(myCourse => myCourse.code_course === course.code_course);

                console.log(`Course ${course.name_course} - Is enrolled:`, isEnrolled);

                // If user has enrolled courses, only show non-enrolled courses in the grid
                // If user has no enrolled courses (or not logged in), show all courses
                const shouldShowCourse = !userHasCourses || !isEnrolled;

                console.log(`Course ${course.name_course} - Should show:`, shouldShowCourse);

                if (shouldShowCourse) {
                    const courseName = course.name_course || 'Untitled Course';
                    const courseDescription = course.description_course || 'No description available';
                    const courseImage = course.cover_img || 'https://via.placeholder.com/350x220';
                    const coursePrice = course.price_course || '0.00';
                    const originalPrice = (parseFloat(coursePrice) * 1.5).toFixed(2);

                    console.log(`Rendering course: ${courseName}`);

                    html += `
                        <div class="course-card">
                            <img src="${courseImage}" alt="${courseName}">
                            <div class="course-card-content">
                                <div class="course-title">${courseName}</div>
                                <div class="course-meta">
                                    <div class="rating">
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                        <i class="fas fa-star"></i>
                                    </div>
                                </div>
                                <div class="course-description">${courseDescription}</div>
                                <div class="course-footer">
                                    <div class="course-price">
                                        <span class="current-price">$${coursePrice}</span>
                                        <span class="original-price">$${originalPrice}</span>
                                    </div>
                                    <a href="https://abilityseminarsgroup.com/course-detail/?course=${course.code_course}"
                                       class="buy-btn">Buy now</a>
                                </div>
                            </div>
                        </div>
                    `;
                }
            });

            if (html === '') {
               const message = userHasCourses
                ? ''
            : 'No courses found in this category.';
             html = `<p style="text-align: center; color: #64748b; grid-column: 1 / -1; padding: 40px;">${message}</p>`;
              
           }

           $('#coursesGrid').html(html);
    }

        // Setup event listeners
        function setupEventListeners() {
            // Category filter buttons
            $('.filter-btn').on('click', function() {
                $('.filter-btn').removeClass('active');
                $(this).addClass('active');
                currentFilter = $(this).data('category');
                renderCoursesGrid();
            });
        }

        // Show/hide loading state
        function showLoading(show) {
            if (show) {
                $('#loading').show();
                $('#coursesGrid').hide();
            } else {
                $('#loading').hide();
                $('#coursesGrid').show();
            }
        }

        // Category name mapping for better display
        function getCategoryDisplayName(category) {
            const categoryMap = {
                'finance': 'Finance',
                'marketing': 'Marketing',
                'personal-development': 'Personal Development',
                'technology': 'Technology',
                'business': 'Business'
            };
            return categoryMap[category] || category;
        }

        /*
         * Busca todos los .course-title y reemplaza el último espacio
         * por un &nbsp; para que la última palabra vaya siempre junto.
         */
        function fixCourseTitleOrphans() {
            document.querySelectorAll('.course-title').forEach(el => {
            // Evitamos procesar dos veces el mismo elemento
            if (el.dataset.orphansFixed) return;
            const text = el.textContent.trim();
            // Solo si hay al menos 2 palabras
            if (text.split(/\s+/).length < 2) return;
            // Reemplaza el último espacio por un &nbsp;
            const html = text.replace(/\s+([^\s]+)$/, '&nbsp;$1');
            el.innerHTML = html;
            el.dataset.orphansFixed = 'true';
            });
        }

        // 1. Procesa los que ya existen en el DOM
        fixCourseTitleOrphans();

        // 2. Observador para elementos que se añaden dinámicamente
        const observer = new MutationObserver((mutations) => {
            // Cada vez que cambie el DOM, vuelve a aplicar el fix
            fixCourseTitleOrphans();
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

    </script>
</body>
</html>
