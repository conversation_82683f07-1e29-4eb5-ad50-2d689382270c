/**
 * ASG Lessons System - Versión Limpia y Refactorizada
 * Sistema de lecciones para AbilitySeminarsGroup
 *
 * Funcionalidades:
 * - Carga de lecciones (video/quiz)
 * - Sistema de progreso con persistencia
 * - Control de acceso y enrollment
 * - Marcado visual de lecciones completadas
 * - Navegación secuencial con bloqueo
 */

// Verificar que estamos en WordPress
if (!defined('ABSPATH')) {
    exit;
}

// Configuración y variables globales
$site_url = get_site_url();
$course_code = isset($_GET['course']) ? sanitize_text_field($_GET['course']) : '';
$lesson_id = isset($_GET['lesson']) ? sanitize_text_field($_GET['lesson']) : '';
$auto_load_first = !$lesson_id && $course_code;


// ===== VERIFICACIÓN ROBUSTA DE ACCESO =====
$current_user = wp_get_current_user();
$user_id = $current_user->ID;
$access_denied = false;
$access_message = '';
$redirect_url = '';
$user_status = 'guest'; // guest, logged_in_not_enrolled, enrolled

// Función para verificar enrollment de forma robusta
function asg_check_user_enrollment($user_id, $course_code) {
    if (!$user_id || !$course_code) {
        return false;
    }

    global $wpdb;

    // Verificar enrollment activo
    $enrollment_check = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$wpdb->prefix}asg_student_enrollments
         WHERE id_user = %d AND code_course = %s AND status = 'active'",
        $user_id, $course_code
    ));

    // Log para debugging (remover en producción)
    error_log("ASG Enrollment Check - User: $user_id, Course: $course_code, Result: " . ($enrollment_check ? 'ENROLLED' : 'NOT_ENROLLED'));

    return $enrollment_check !== null;
}

// Verificar estado del usuario y acceso al curso
if (!$user_id) {
    // Usuario no logueado
    $user_status = 'guest';
    $access_denied = true;
    $access_message = 'You need to sign in to access course lessons.';
    $redirect_url = '/wp-login.php?redirect_to=' . urlencode($_SERVER['REQUEST_URI']);

} elseif ($course_code) {
    // Usuario logueado - verificar enrollment
    if (asg_check_user_enrollment($user_id, $course_code)) {
        // Usuario inscrito - acceso permitido
        $user_status = 'enrolled';
        $access_denied = false;
    } else {
        // Usuario logueado pero no inscrito
        $user_status = 'logged_in_not_enrolled';
        $access_denied = true;
        $access_message = 'You need to purchase this course to access the lessons.';
        $redirect_url = '/online-courses/?course=' . urlencode($course_code);
    }
} else {
    // No hay código de curso
    $access_denied = true;
    $access_message = 'Invalid course access.';
    $redirect_url = '/online-courses/';
}

function asg_render_lessons_page() {
    global $site_url, $course_code, $lesson_id, $auto_load_first, $access_denied, $access_message;
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="wp-nonce" content="<?php echo wp_create_nonce('wp_rest'); ?>">
    <title>Lecciones - <?php echo esc_html($course_code); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Outfit:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Lato:wght@400;700&display=swap" rel="stylesheet">

    <!-- html2canvas for certificate download -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

    <style>
        :root {
            --primary-blue: #0C1B41;
            --secondary-blue: #2E5F8A;
            --accent-blue: #4A90E2;
            --accent-yellow: #ffc107;
            --success-color: #28a745;
            --light-gray: #f8f9fa;
            --text-dark: #333;
            --text-light: #666;
            --transition: all 0.3s ease;
        }

		#lessonNumber{
			font-size: 22px;
			
		}
		
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

		.nav{
			display:none;
		}
        body {
            font-family: 'Outfit', sans-serif;
            background-color: #f5f7fa;
            color: var(--text-dark);
            line-height: 1.6;
        }

        /* Header principal */
        .main-header {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            color: white;
            padding: 1rem 0;
            position: relative;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .course-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .back-arrow {
            color: white;
            text-decoration: none;
            font-size: 1.2rem;
            padding: 0.5rem;
            border-radius: 50%;
            transition: var(--transition);
        }

        .back-arrow:hover {
            background: rgba(255,255,255,0.1);
            color: white;
        }

        .course-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin: 0;
        }

        .progress-section {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .progress-container {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .progress-bar {
            width: 200px;
            height: 8px;
            background: rgba(255,255,255,0.2);
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: var(--accent-yellow);
            border-radius: 4px;
            transition: width 0.3s ease;
            width: 0%;
            display: block;
            position: relative;
            z-index: 1;
        }

        .progress-text {
            font-weight: 600;
            font-size: 0.9rem;
        }

        .user-section {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .username {
            font-weight: 500;
        }

        .btn-courses {
            background: var(--accent-yellow);
            color: var(--primary-blue);
            border: none;
            padding: 0.5rem 1.5rem;
            border-radius: 25px;
            font-weight: 600;
            text-decoration: none;
            transition: var(--transition);
        }

        .btn-courses:hover {
            background: #ffb300;
            color: var(--primary-blue);
            transform: translateY(-1px);
        }

        /* Lesson info section */
        .lesson-info-section {
            background: white;
            padding: 1.5rem 0;
            border-bottom: 1px solid #e9ecef;
        }

        .lesson-info-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .lesson-meta {
            display: flex;
            align-items: center;
            gap: 2rem;
        }

        .lesson-number {
            font-size: 1rem;
            color: var(--text-light);
            font-weight: 500;
        }

        .lesson-title-main {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--text-dark);
            margin: 0;
        }

        .module-info {
            font-size: 0.9rem;
            color: var(--text-light);
            font-weight: 500;
        }

        .btn-course-content {
            background: var(--primary-blue);
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            text-decoration: none;
            transition: var(--transition);
        }

        .btn-course-content:hover {
            background: var(--secondary-blue);
            color: white;
            transform: translateY(-1px);
        }

        /* Main content layout */
        .main-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .lesson-content-wrapper {
            background: white;
            border-radius: 12px;
            padding: 3rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .lesson-header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .lesson-type-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 1rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .lesson-type-badge.lesson-type-video {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .lesson-type-badge.lesson-type-quiz {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }

        .lesson-type-badge.lesson-type-text {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }
		
        .lesson-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-blue);
            margin-bottom: 1rem;
        }

        .lesson-title-main {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary-blue);
            margin-bottom: 0.5rem;
            text-shadow: none;
        }

        .lesson-description {
            font-size: 1.1rem;
            color: var(--text-light);
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
        }

        /* Content sections */
        .content-section {
            margin-bottom: 3rem;
        }

        .content-image {
            text-align: center;
            margin: 2rem 0;
        }

        .content-image img {
            max-width: 60%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .content-text {
            font-size: 1.1rem;
            line-height: 1.8;
            color: var(--text-dark);
            margin-bottom: 2rem;
        }

        .content-text p {
            margin-bottom: 1.5rem;
        }

        .content-text strong {
            color: var(--primary-blue);
            font-weight: 600;
        }

        /* Estilos para lecciones de texto */
        .text-lesson-container {
            max-width: 100%;
        }

        .lesson-image-container {
            text-align: center;
            margin: 2rem 0;
            position: relative;
        }

        .lesson-image {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .image-caption {
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.7);
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
			display: none;
        }

        .lesson-text-content {
            font-size: 1.1rem;
            line-height: 1.8;
            color: var(--text-dark);
        }

        .lesson-text-content p {
            margin-bottom: 1.5rem;
        }

        .lesson-text-content strong {
            color: var(--primary-blue);
            font-weight: 600;
        }

        /* Layout imagen + primer párrafo */
        .image-text-layout {
            display: flex;
            gap: 2rem;
            margin-bottom: 2rem;
            align-items: flex-start;
        }

        .image-text-layout .lesson-image-container {
            flex: 0 0 45%;
            margin: 0;
        }

        .image-text-layout .first-paragraph {
            flex: 1;
            font-size: 1.1rem;
            line-height: 1.8;
            color: var(--text-dark);
            margin: 0;
        }

        .remaining-content {
            font-size: 1.1rem;
            line-height: 1.8;
            color: var(--text-dark);
        }

        .remaining-content p {
            margin-bottom: 1.5rem;
        }

        .remaining-content strong {
            color: var(--primary-blue);
            font-weight: 600;
        }

        .highlight-box {
            background: #f8f9fa;
            border-left: 4px solid var(--accent-blue);
            padding: 1.5rem;
            margin: 2rem 0;
            border-radius: 0 8px 8px 0;
        }

        .highlight-box h4 {
            color: var(--primary-blue);
            margin-bottom: 1rem;
            font-weight: 600;
        }

        .highlight-box p {
            margin-bottom: 0.5rem;
        }

       
        /* Navigation section */
        .lesson-navigation {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-button {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            text-decoration: none;
            transition: var(--transition);
            cursor: pointer;
        }

        .nav-button.prev {
            background: #f8f9fa;
            color: var(--text-dark);
        }

        .nav-button.prev:hover {
            background: #e9ecef;
            color: var(--text-dark);
        }

        .nav-button.next {
            background: var(--accent-blue);
            color: white;
        }

        .nav-button.next:hover {
            background: var(--secondary-blue);
            color: white;
            transform: translateY(-1px);
        }

        .nav-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .nav-button:disabled:hover {
            transform: none;
        }

        .completion-button {
            background: #28a745;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .completion-button:hover:not(:disabled) {
            background: #218838;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }

        .completion-button:disabled {
            opacity: 0.7;
            cursor: not-allowed;
        }

        .completion-button.completed {
            background: #6c757d;
        }

        .certificate-button {
            background: linear-gradient(135deg, var(--accent-blue), var(--secondary-blue));
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            display: none; /* Hidden by default */
            align-items: center;
            gap: 0.5rem;
        }

        .certificate-button:hover {
            background: linear-gradient(135deg, var(--secondary-blue), var(--primary-blue));
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
        }

        .certificate-button.show {
            display: flex;
        }

        /* Modal Styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            border-radius: 12px;
            max-width: 800px;
            width: 90%;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem 2rem;
            border-bottom: 1px solid #e9ecef;
            background: var(--primary-blue);
            color: white;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .modal-close {
            background: none;
            border: none;
            color: white;
            font-size: 2rem;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: var(--transition);
        }

        .modal-close:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .modal-body {
            padding: 2rem;
            max-height: 60vh;
            overflow-y: auto;
        }

        .course-module {
            margin-bottom: 1.5rem;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
        }

        .module-header {
            background: #f8f9fa;
            padding: 1rem 1.5rem;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: var(--transition);
        }

        .module-header:hover {
            background: #e9ecef;
        }

        .module-title {
            font-weight: 600;
            color: var(--text-dark);
            margin: 0;
        }

        .module-chevron {
            transition: transform 0.3s ease;
            color: var(--text-light);
        }

        .module-chevron.rotated {
            transform: rotate(90deg);
        }

        .module-lessons {
            background: white;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .module-lessons.show {
            max-height: 500px;
        }

        .lesson-item {
            padding: 0.75rem 1.5rem;
            border-bottom: 1px solid #f1f3f4;
            display: flex;
            align-items: center;
            gap: 1rem;
            cursor: pointer;
            transition: var(--transition);
        }

        .lesson-item:hover {
            background: #f8f9fa;
        }

        .lesson-item:last-child {
            border-bottom: none;
        }

        .lesson-icon {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .lesson-info {
            flex: 1;
        }

        .lesson-name {
            font-weight: 500;
            color: var(--text-dark);
            margin-bottom: 0.25rem;
        }

        .lesson-type {
            font-size: 0.85rem;
            color: var(--text-light);
            text-transform: capitalize;
        }

        .lesson-status {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .lesson-status.completed {
            color: #28a745;
        }

        .lesson-status.locked {
            color: #6c757d;
        }

        .lesson-status.available {
            color: var(--accent-blue);
        }

        .lesson-counter {
            text-align: center;
            font-size: 1rem;
            color: var(--text-light);
            font-weight: 500;
        }

        .completion-button {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: var(--success-color);
            color: white;
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
        }

        .completion-button:hover {
            background: #218838;
            transform: translateY(-1px);
        }

        .completion-button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }

        .completion-button:disabled:hover {
            transform: none;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }

            .course-info {
                flex-direction: column;
                gap: 0.5rem;
            }

            .course-title {
                font-size: 1rem;
            }

            .progress-bar {
                width: 150px;
            }

            .lesson-info-content {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }

            .lesson-meta {
                flex-direction: column;
                gap: 0.5rem;
            }

            .main-content {
                padding: 1rem;
            }

            .lesson-content-wrapper {
                padding: 2rem 1.5rem;
            }

            .lesson-title {
                font-size: 1.5rem;
            }

            .content-image img {
                max-width: 90%;
            }

            /* Responsive para layout imagen + texto */
            .image-text-layout {
                flex-direction: column;
                gap: 1rem;
            }

            .image-text-layout .lesson-image-container {
                flex: none;
            }

            .lesson-navigation {
                flex-direction: column;
                gap: 1rem;
            }

            .nav-button {
                width: 100%;
                justify-content: center;
            }

            .completion-button {
                width: 100%;
                justify-content: center;
            }

            .certificate-button {
                width: 100%;
                justify-content: center;
                margin-top: 0.5rem;
            }
        }

        /* Comments Section Styles */
        .comment-section {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .comment-section h4 {
            color: var(--primary-blue);
            font-weight: 600;
            margin-bottom: 1.5rem;
        }

        .comment-section .form-control {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            font-family: 'Outfit', sans-serif;
            transition: var(--transition);
        }

        .comment-section .form-control:focus {
            border-color: var(--accent-blue);
            box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
        }

        .comment-section .btn {
            background: var(--primary-blue) !important;
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 25px;
            font-weight: 600;
            transition: var(--transition);
        }

        .comment-section .btn:hover {
            background: var(--secondary-blue) !important;
            transform: translateY(-1px);
        }

        .comment-section .list-group-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 1rem;
            padding: 1.5rem;
        }

        .comment-section .list-group-item strong {
            color: var(--primary-blue);
            font-weight: 600;
        }

        .comment-section .list-group-item p {
            color: var(--text-dark);
            margin: 0.5rem 0;
            line-height: 1.6;
        }

        .comment-section .list-group-item small {
            color: var(--text-light);
            font-size: 0.9rem;
        }

        /* Loading states */
        .loading-state {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 200px;
            flex-direction: column;
            gap: 1rem;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid var(--primary-blue);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Video container */
        .video-container {
            position: relative;
            width: 100%;
            max-width: 800px;
            margin: 2rem auto;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .video-container iframe {
            width: 100%;
            height: 450px;
            border: none;
        }

        /* Quiz styles */
        .quiz-container {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 2rem;
            margin: 2rem 0;
        }

        .quiz-question {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary-blue);
            margin-bottom: 1.5rem;
        }

        .quiz-options {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .quiz-option {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            cursor: pointer;
            transition: var(--transition);
        }

        .quiz-option:hover {
            border-color: var(--accent-blue);
            background: #f8f9fa;
        }

        .quiz-option.selected {
            border-color: var(--accent-blue);
            background: rgba(74, 144, 226, 0.1);
        }

        .quiz-option.incorrect {
            border-color: #dc3545 !important;
            background-color: #f8d7da !important;
            color: #721c24 !important;
        }

        .quiz-option.correct {
            border-color: #28a745 !important;
            background-color: #d4edda !important;
            color: #155724 !important;
        }

        .alert {
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            border: 1px solid transparent;
        }

        .alert-danger {
            color: #721c24;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }

        .alert-success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }

        /* Notification styles */
        .notification {
            position: fixed;
            top: 100px;
            right: 20px;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 1001;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: var(--success-color);
        }

        .notification.error {
            background: #dc3545;
        }

        .notification.warning {
            background: #ffc107;
            color: var(--text-dark);
        }

        /* Quiz Styles */
        .quiz-lesson-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .quiz-intro {
            text-align: center;
            padding: 2rem;
            background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
            border-radius: 12px;
            color: white;
            margin-bottom: 2rem;
        }

        .quiz-intro-content h3 {
            font-size: 1.8rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }

        .quiz-description {
            font-size: 1.1rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .quiz-stats {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1rem;
            font-weight: 500;
        }

        .start-quiz-btn {
            padding: 1rem 2rem;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            transition: var(--transition);
        }

        .start-quiz-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .quiz-content {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .quiz-header {
            margin-bottom: 2rem;
        }

        .quiz-progress {
            background: #e9ecef;
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 1rem;
        }

        .progress-bar {
            background: linear-gradient(90deg, var(--primary-blue), var(--accent-blue));
            
            transition: width 0.3s ease;
        }

        .question-counter {
            text-align: center;
            font-weight: 600;
            color: var(--text-light);
        }

        .quiz-question-content {
            margin-bottom: 2rem;
        }

        .question-text {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 1rem;
            line-height: 1.4;
        }

        .instruction-text {
            color: var(--text-light);
            margin-bottom: 1.5rem;
            font-style: italic;
        }

        .quiz-options {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .quiz-option {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem 1.5rem;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .quiz-option:hover {
            border-color: var(--accent-blue);
            background: #f0f8ff;
        }

        .quiz-option.selected {
            border-color: var(--primary-blue);
            background: #e3f2fd;
        }

        .quiz-option input {
            margin: 0;
        }

        .option-text {
            flex: 1;
            font-size: 1rem;
            line-height: 1.4;
        }

        .quiz-navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #e9ecef;
        }

        .quiz-results {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .results-header {
            margin-bottom: 2rem;
        }

        .results-header.passed {
            color: #28a745;
        }

        .results-header.failed {
            color: #dc3545;
        }

        .score-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 2rem;
            font-weight: 700;
            color: white;
        }

        .results-header.passed .score-circle {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .results-header.failed .score-circle {
            background: linear-gradient(135deg, #dc3545, #fd7e14);
        }

        .results-actions {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin-top: 2rem;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .main-content {
                padding: 1rem;
            }

            .lesson-content-wrapper {
                padding: 2rem 1.5rem;
            }

            .lesson-navigation {
                flex-direction: column;
                gap: 1rem;
            }

            .nav-button {
                width: 100%;
                justify-content: center;
            }

            .lesson-counter {
                order: -1;
                text-align: center;
            }

            .quiz-stats {
                flex-direction: column;
                gap: 1rem;
            }

            .quiz-navigation {
                flex-direction: column;
                gap: 1rem;
            }

            .results-actions {
                flex-direction: column;
            }
        }

        /* Course Completion Modal & Certificate Styles */
        .completion-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 2000;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .completion-modal-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .completion-modal-content {
            background: white;
            border-radius: 20px;
            max-width: 900px;
            width: 95%;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            transform: scale(0.8) translateY(50px);
            transition: all 0.3s ease;
        }

        .completion-modal-overlay.show .completion-modal-content {
            transform: scale(1) translateY(0);
        }

        .completion-celebration {
            position: relative;
            padding: 3rem 2rem;
            text-align: center;
            overflow: hidden;
        }

        /* Confetti Animation */
        .confetti-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }

        .confetti {
            position: absolute;
            width: 10px;
            height: 10px;
            background: var(--accent-yellow);
            animation: confetti-fall 3s linear infinite;
        }

        .confetti:nth-child(1) { left: 10%; animation-delay: 0s; background: var(--accent-blue); }
        .confetti:nth-child(2) { left: 20%; animation-delay: 0.2s; background: var(--accent-yellow); }
        .confetti:nth-child(3) { left: 30%; animation-delay: 0.4s; background: var(--primary-blue); }
        .confetti:nth-child(4) { left: 40%; animation-delay: 0.6s; background: var(--secondary-blue); }
        .confetti:nth-child(5) { left: 50%; animation-delay: 0.8s; background: var(--accent-blue); }
        .confetti:nth-child(6) { left: 60%; animation-delay: 1s; background: var(--accent-yellow); }
        .confetti:nth-child(7) { left: 70%; animation-delay: 1.2s; background: var(--primary-blue); }
        .confetti:nth-child(8) { left: 80%; animation-delay: 1.4s; background: var(--secondary-blue); }
        .confetti:nth-child(9) { left: 90%; animation-delay: 1.6s; background: var(--accent-blue); }
        .confetti:nth-child(10) { left: 95%; animation-delay: 1.8s; background: var(--accent-yellow); }

        @keyframes confetti-fall {
            0% {
                transform: translateY(-100vh) rotate(0deg);
                opacity: 1;
            }
            100% {
                transform: translateY(100vh) rotate(360deg);
                opacity: 0;
            }
        }

        .completion-header {
            margin-bottom: 2rem;
            position: relative;
            z-index: 1;
        }

        .completion-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-20px);
            }
            60% {
                transform: translateY(-10px);
            }
        }

        .completion-header h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-blue);
            margin-bottom: 0.5rem;
            font-family: 'Outfit', sans-serif;
        }

        .completion-header p {
            font-size: 1.2rem;
            color: var(--text-light);
            margin: 0;
        }

        /* Certificate Styles */
        .certificate-container {
            margin: 2rem 0;
            position: relative;
            z-index: 1;
        }

        .certificate {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 15px;
            padding: 2rem;
            margin: 0 auto;
            max-width: 700px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .certificate-border {
            border: 3px solid;
            border-image: linear-gradient(135deg, var(--primary-blue), var(--accent-blue), var(--secondary-blue)) 1;
            border-radius: 12px;
            padding: 2rem;
            position: relative;
        }

        .certificate-border::before {
            content: '';
            position: absolute;
            top: -3px;
            left: -3px;
            right: -3px;
            bottom: -3px;
            background: linear-gradient(135deg, var(--primary-blue), var(--accent-blue), var(--secondary-blue));
            border-radius: 15px;
            z-index: -1;
        }

        .certificate-content {
            background: white;
            border-radius: 8px;
            padding: 2rem;
            text-align: center;
        }

        .certificate-header {
            margin-bottom: 2rem;
        }

        .certificate-header h3 {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary-blue);
            margin-bottom: 1rem;
            letter-spacing: 2px;
            text-transform: uppercase;
        }

        .certificate-logo {
            font-size: 3rem;
            color: var(--accent-yellow);
        }

        .certificate-body {
            margin: 2rem 0;
        }

        .certificate-text {
            font-size: 1.1rem;
            color: var(--text-light);
            margin: 0.5rem 0;
        }

        .student-name, .course-name {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary-blue);
            margin: 1rem 0;
            text-decoration: underline;
            text-decoration-color: var(--accent-blue);
            text-underline-offset: 8px;
        }

        .completion-date {
            font-size: 1rem;
            color: var(--text-light);
            margin-top: 1.5rem;
        }

        .certificate-footer {
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 2px solid var(--accent-blue);
        }

        .signature-line {
            text-align: center;
        }

        .signature {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--primary-blue);
            margin-bottom: 0.5rem;
        }

        .signature-title {
            font-size: 0.9rem;
            color: var(--text-light);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        /* Completion Actions */
        .completion-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 2rem;
            position: relative;
            z-index: 1;
        }

        .btn-download-certificate,
        .btn-share-achievement,
        .btn-close-modal {
            padding: 0.75rem 2rem;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-download-certificate {
            background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
            color: white;
        }

        .btn-download-certificate:hover {
            background: linear-gradient(135deg, var(--secondary-blue), var(--accent-blue));
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(12, 27, 65, 0.3);
        }

        .btn-share-achievement {
            background: linear-gradient(135deg, var(--accent-blue), var(--secondary-blue));
            color: white;
        }

        .btn-share-achievement:hover {
            background: linear-gradient(135deg, var(--secondary-blue), var(--primary-blue));
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(74, 144, 226, 0.3);
        }

        .btn-close-modal {
            background: var(--accent-yellow);
            color: var(--primary-blue);
        }

        .btn-close-modal:hover {
            background: #ffb300;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3);
        }

        /* Responsive Design for Certificate Modal */
        @media (max-width: 768px) {
            .completion-modal-content {
                width: 98%;
                margin: 1rem;
            }

            .completion-celebration {
                padding: 2rem 1rem;
            }

            .completion-header h2 {
                font-size: 2rem;
            }

            .certificate {
                padding: 1rem;
            }

            .certificate-border {
                padding: 1rem;
            }

            .certificate-content {
                padding: 1.5rem;
            }

            .certificate-header h3 {
                font-size: 1.4rem;
            }

            .student-name, .course-name {
                font-size: 1.4rem;
            }

            .completion-actions {
                flex-direction: column;
                align-items: center;
            }

            .btn-download-certificate,
            .btn-share-achievement,
            .btn-close-modal {
                width: 100%;
                max-width: 300px;
                justify-content: center;
            }
        }

    </style>
</head>
<body>
    </style>
</head>
<body>
    <?php if ($access_denied): ?>
        <!-- Estado de acceso denegado - Verificación Robusta -->
        <div class="container-fluid d-flex justify-content-center align-items-center min-vh-100" style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
            <div class="text-center" style="max-width: 600px; padding: 3rem; background: white; border-radius: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">

                <?php if ($user_status === 'guest'): ?>
                    <!-- Usuario no logueado -->
                    <div class="mb-4">
                        <i class="bi bi-person-x-fill" style="font-size: 4rem; color: #ffc107;"></i>
                    </div>
                    <h2 class="mb-3" style="color: var(--primary-blue);">Sign In Required</h2>
                    <p class="mb-4" style="color: var(--text-light); font-size: 1.1rem;"><?php echo esc_html($access_message); ?></p>
                    <div class="d-flex gap-3 justify-content-center flex-wrap">
                        <a href="<?php echo esc_url($redirect_url); ?>" class="btn btn-lg" style="background: var(--primary-blue); color: white; padding: 0.75rem 2rem; border-radius: 25px;">
                            <i class="bi bi-box-arrow-in-right me-2"></i>Sign In
                        </a>
                        <a href="/register/" class="btn btn-lg btn-outline-secondary" style="padding: 0.75rem 2rem; border-radius: 25px;">
                            <i class="bi bi-person-plus me-2"></i>Create Account
                        </a>
                    </div>

                <?php elseif ($user_status === 'logged_in_not_enrolled'): ?>
                    <!-- Usuario logueado pero no inscrito -->
                    <div class="mb-4">
                        <i class="bi bi-cart-x-fill" style="font-size: 4rem; color: #dc3545;"></i>
                    </div>
                    <h2 class="mb-3" style="color: var(--primary-blue);">Course Purchase Required</h2>
                    <p class="mb-4" style="color: var(--text-light); font-size: 1.1rem;"><?php echo esc_html($access_message); ?></p>
                    <div class="alert alert-info mb-4" style="border-radius: 15px;">
                        <i class="bi bi-info-circle me-2"></i>
                        <strong>Hello <?php echo esc_html($current_user->display_name); ?>!</strong>
                        You're signed in but need to purchase this course to access the lessons.
                    </div>
                    <div class="d-flex gap-3 justify-content-center flex-wrap">
                        <a href="<?php echo esc_url($redirect_url); ?>" class="btn btn-lg" style="background: var(--accent-blue); color: white; padding: 0.75rem 2rem; border-radius: 25px;">
                            <i class="bi bi-cart-plus me-2"></i>Purchase Course
                        </a>
                        <a href="/online-courses/" class="btn btn-lg btn-outline-secondary" style="padding: 0.75rem 2rem; border-radius: 25px;">
                            <i class="bi bi-grid me-2"></i>Browse All Courses
                        </a>
                    </div>

                <?php else: ?>
                    <!-- Error genérico -->
                    <div class="mb-4">
                        <i class="bi bi-exclamation-triangle-fill" style="font-size: 4rem; color: #ffc107;"></i>
                    </div>
                    <h2 class="mb-3" style="color: var(--primary-blue);">Access Restricted</h2>
                    <p class="mb-4" style="color: var(--text-light); font-size: 1.1rem;"><?php echo esc_html($access_message); ?></p>
                    <div class="d-flex gap-3 justify-content-center flex-wrap">
                        <a href="/online-courses/" class="btn btn-lg" style="background: var(--primary-blue); color: white; padding: 0.75rem 2rem; border-radius: 25px;">
                            <i class="bi bi-house me-2"></i>Go to Courses
                        </a>
                    </div>
                <?php endif; ?>

                <!-- Información adicional -->
                <div class="mt-4 pt-3" style="border-top: 1px solid #e9ecef;">
                    <small class="text-muted">
                        <i class="bi bi-shield-check me-1"></i>
                        Secure access verification by ASG Learning Platform
                    </small>
                </div>
            </div>
        </div>

        <!-- Script para redirección automática opcional -->
        <script>
            // Redirección automática después de 10 segundos (opcional)
            // setTimeout(() => {
            //     window.location.href = '<?php echo esc_js($redirect_url); ?>';
            // }, 10000);
        </script>
    <?php else: ?>

        <!-- Header principal -->
        <header class="main-header">
            <div class="header-content">
                <div class="course-info">
                    <a href="/my-programs/" class="back-arrow">
                        <i class="bi bi-arrow-left"></i>
                    </a>
                    <h1 class="course-title" id="courseTitle">How to Interview for a Job: A Guide to Personal Marketing</h1>
                </div>

                <div class="progress-section">
                    <div class="progress-container">
                        <span class="progress-text">Progress</span>
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                        <span class="progress-text" id="progressText">45%</span>
                    </div>
                </div>

                <div class="user-section">
                    <span class="username" id="username">Username</span>
                    <i class="bi bi-person-circle" style="font-size: 1.5rem;"></i>
                    <a href="/my-programs/" class="btn-courses">Go to my Courses</a>
                </div>
            </div>
        </header>

        <!-- Lesson info section -->
        <section class="lesson-info-section">
            <div class="lesson-info-content">
                <div class="lesson-meta">
                    <span class="lesson-number" id="lessonNumber">Lesson N.1</span>
                    <h2 class="lesson-title-main" id="lessonTitle">What is Personal Marketing</h2>
                    <span class="module-info" id="moduleInfo">Module 1.1</span>
                </div>
                <button class="btn-course-content" id="btnCourseContent">See Course Content</button>
            </div>
        </section>

        <!-- Main content -->
        <main class="main-content">
            <div class="lesson-content-wrapper">
                <div id="lessonContentArea">
                    <div class="loading-state">
                        <div class="spinner"></div>
                        <span>Loading Content...</span>
                    </div>
                </div>
            </div>

            <!-- Comments Section -->
            <div class="comment-section mt-5" id="commentsSection">
                <h4><i class="bi bi-chat-dots me-2"></i>Comments</h4>
                <form id="commentForm" class="mb-3">
                    <textarea id="commentText" class="form-control mb-2" rows="3" placeholder="Write your comment..." required></textarea>
                    <button type="submit" class="btn" style="background: #0C1B40; color: white;">Send</button>
                </form>
                <div id="commentsList" class="list-group">
                    <!-- Comments will be injected here -->
                </div>
            </div>

            <!-- Navigation -->
            <nav class="lesson-navigation">
                <button class="nav-button prev" id="btnPrevLesson">
                    <i class="bi bi-arrow-left"></i>
                    Previous Lesson
                </button>

                <div class="lesson-counter" id="lessonCounter">1 of 3 Lessons</div>

                <div style="display: flex; align-items: center; gap: 1rem;">
                    <button class="completion-button" id="btnMarkComplete">
                        <i class="bi bi-check-circle"></i>
                        Mark as Complete
                    </button>

                    <button class="certificate-button" id="btnViewCertificate" onclick="showCourseCompletionModal()">
                        <i class="bi bi-award"></i>
                        View Certificate
                    </button>

                    <button class="nav-button next" id="btnNextLesson">
                        Next Lesson
                        <i class="bi bi-arrow-right"></i>
                    </button>
                </div>
            </nav>
        </main>

        <!-- Modal de contenido del curso -->
        <div id="courseContentModal" class="modal-overlay" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Course Content</h3>
                    <button class="modal-close" onclick="closeCourseContentModal()">&times;</button>
                </div>
                <div class="modal-body" id="courseContentBody">
                    <div class="loading-state">
                        <div class="spinner"></div>
                        <span>Loading course content...</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Notificaciones -->
        <div id="notificationContainer"></div>

        <!-- Bootstrap JS -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

        <script>
            /**
             * ASG Lessons System - JavaScript Limpio y Refactorizado
             * Funciones organizadas y reutilizables
             */

            // Prevenir conflictos con otros scripts
            (function() {
                'use strict';

            // ===== CONFIGURACIÓN GLOBAL =====
            const ASG_CONFIG = {
                API_BASE: '<?php echo $site_url; ?>/wp-json/asg/v1',
                COURSE_CODE: '<?php echo esc_js($course_code); ?>',
                LESSON_ID: '<?php echo esc_js($lesson_id); ?>',
                AUTO_LOAD_FIRST: <?php echo $auto_load_first ? 'true' : 'false'; ?>,
                USER_ID: <?php echo get_current_user_id(); ?>,
                USER_NAME: '<?php echo esc_js(wp_get_current_user()->display_name ?: wp_get_current_user()->user_login ?: "Estudiante"); ?>'
            };

            // Fallback: obtener parámetros de URL si no están definidos
            if (!ASG_CONFIG.COURSE_CODE || !ASG_CONFIG.LESSON_ID) {
                const urlParams = new URLSearchParams(window.location.search);

                if (!ASG_CONFIG.COURSE_CODE) {
                    ASG_CONFIG.COURSE_CODE = urlParams.get('course') || '';
                }

                if (!ASG_CONFIG.LESSON_ID) {
                    ASG_CONFIG.LESSON_ID = urlParams.get('lesson') || '';
                }

                // Actualizar AUTO_LOAD_FIRST basado en parámetros
                ASG_CONFIG.AUTO_LOAD_FIRST = !ASG_CONFIG.LESSON_ID && ASG_CONFIG.COURSE_CODE;
            }

            console.log('Configuración inicial:', ASG_CONFIG);

            // ===== ESTADO GLOBAL =====
            let asgState = {
                currentCourse: null,
                currentLesson: null,
                courseProgress: null,
                lessonsList: [],
                isLoading: false
            };

            // ===== FUNCIONES UTILITARIAS =====

            /**
             * Realizar petición API con manejo de errores
             */
            async function apiRequest(endpoint, options = {}) {
                const url = `${ASG_CONFIG.API_BASE}${endpoint}`;

                // Get nonce from meta tag
                const nonce = document.querySelector('meta[name="wp-nonce"]')?.content;

                const defaultOptions = {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };

                // Add nonce to headers if available and not already present
                if (nonce && !options.headers?.['X-WP-Nonce']) {
                    defaultOptions.headers['X-WP-Nonce'] = nonce;
                }

                try {
                    const response = await fetch(url, { ...defaultOptions, ...options });

                    if (!response.ok) {
                        // Log more details for debugging
                        console.error(`API Request failed: ${response.status} ${response.statusText}`, {
                            url,
                            method: options.method || 'GET',
                            headers: { ...defaultOptions.headers, ...options.headers }
                        });
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    return await response.json();
                } catch (error) {
                    console.error('API Error:', error);
                    throw error;
                }
            }

            /**
             * Mostrar notificación al usuario
             */
            function showNotification(message, type = 'success', duration = 3000) {
                const container = document.getElementById('notificationContainer');
                const notification = document.createElement('div');

                notification.className = `notification ${type}`;
                notification.innerHTML = `
                    <i class="bi bi-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                    ${message}
                `;

                container.appendChild(notification);

                // Mostrar con animación
                setTimeout(() => notification.classList.add('show'), 100);

                // Ocultar después del tiempo especificado
                setTimeout(() => {
                    notification.classList.remove('show');
                    setTimeout(() => container.removeChild(notification), 300);
                }, duration);
            }

            /**
             * Mostrar mensaje de selección de lección
             */
            function showLessonSelectionMessage() {
                const contentArea = document.getElementById('lessonContentArea');
                if (contentArea) {
                    contentArea.innerHTML = `
                        <div class="lesson-selection-message" style="
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            justify-content: center;
                            min-height: 400px;
                            text-align: center;
                            padding: 3rem;
                        ">
                            <i class="bi bi-book" style="font-size: 4rem; color: var(--primary-blue); margin-bottom: 1.5rem;"></i>
                            <h3 style="color: var(--primary-blue); margin-bottom: 1rem;">Selecciona una lección</h3>
                            <p style="color: var(--text-light); font-size: 1.1rem; margin-bottom: 2rem;">
                                Elige una lección del menú lateral para comenzar tu aprendizaje.
                            </p>
                            <button onclick="startFromFirstLesson()" class="btn btn-lg" style="
                                background: var(--primary-blue);
                                color: white;
                                padding: 0.75rem 2rem;
                                border-radius: 25px;
                            ">
                                <i class="bi bi-play-fill me-2"></i>Comenzar desde la primera lección
                            </button>
                        </div>
                    `;
                }
            }

            /**
             * Comenzar desde la primera lección disponible
             */
            function startFromFirstLesson() {
                if (!asgState.courseProgress || !asgState.lessonsList.length) {
                    showNotification('No hay lecciones disponibles', 'warning');
                    return;
                }

                // Encontrar la primera lección no completada
                const completedLessons = asgState.courseProgress.completed_lessons_list || [];
                let firstAvailableLesson = null;

                // Buscar primera lección no completada
                for (const lesson of asgState.lessonsList) {
                    const lessonId = parseInt(lesson.id);
                    if (!completedLessons.includes(lessonId)) {
                        firstAvailableLesson = lesson;
                        break;
                    }
                }

                // Si todas están completadas, ir a la primera
                if (!firstAvailableLesson && asgState.lessonsList.length > 0) {
                    firstAvailableLesson = asgState.lessonsList[0];
                }

                if (firstAvailableLesson) {
                    console.log('🎯 Iniciando desde primera lección disponible:', firstAvailableLesson.title);
                    navigateToLesson(firstAvailableLesson.id);
                } else {
                    showNotification('No se encontraron lecciones disponibles', 'error');
                }
            }

            /**
             * Actualizar barra de progreso
             */
            function updateProgressBar(progressData) {
                console.log('📊 Updating progress bar:', progressData);

                // Barra de progreso del header
                const progressText = document.getElementById('progressText');
                const progressFill = document.getElementById('progressFill');

                console.log('🔍 Progress elements found:', {
                    progressText: !!progressText,
                    progressFill: !!progressFill,
                    percentage: progressData.progress_percentage
                });

                if (progressText && progressData.progress_percentage !== undefined) {
                    progressText.textContent = `${progressData.progress_percentage}%`;
                    console.log('✅ Progress text updated to:', progressText.textContent);
                }

                if (progressFill && progressData.progress_percentage !== undefined) {
                    progressFill.style.width = `${progressData.progress_percentage}%`;
                    console.log('✅ Progress fill updated to:', progressFill.style.width);
                }

                // Barra de progreso del sidebar
                const sidebarProgressText = document.getElementById('sidebarProgressText');
                const sidebarProgressFill = document.getElementById('sidebarProgressFill');

                if (sidebarProgressText && progressData.progress_percentage !== undefined) {
                    sidebarProgressText.textContent = `${progressData.progress_percentage}%`;
                }

                if (sidebarProgressFill && progressData.progress_percentage !== undefined) {
                    sidebarProgressFill.style.width = `${progressData.progress_percentage}%`;
                }

                // Show/hide certificate button based on completion
                const certificateBtn = document.getElementById('btnViewCertificate');
                if (certificateBtn && progressData.progress_percentage !== undefined) {
                    if (progressData.progress_percentage >= 100) {
                        certificateBtn.classList.add('show');
                        console.log('🏆 Certificate button shown - course completed!');
                    } else {
                        certificateBtn.classList.remove('show');
                    }
                }
            }

            /**
             * Obtener ID de lección actual desde URL
             */
            function getCurrentLessonId() {
                const urlParams = new URLSearchParams(window.location.search);
                return urlParams.get('lesson');
            }

            /**
             * Check if a lesson is locked (Sequential Learning System)
             */
            function isLessonLocked(lessonId) {
                if (!asgState.courseProgress || !asgState.lessonsList) {
      
                    return false;
                }

                const completedLessons = asgState.courseProgress.completed_lessons_list || [];
                const lessonIndex = asgState.lessonsList.findIndex(lesson => parseInt(lesson.id) === parseInt(lessonId));

                // First lesson is always unlocked
                if (lessonIndex === 0) {
                  
                    return false;
                }

                // Check if previous lesson is completed
                const previousLesson = asgState.lessonsList[lessonIndex - 1];
                if (!previousLesson) {
                   
                    return false;
                }

                const isPreviousCompleted = completedLessons.includes(parseInt(previousLesson.id));

             

                return !isPreviousCompleted;
            }

            /**
             * Show locked lesson message
             */
            function showLockedLessonMessage(lessonId) {
                const lessonIndex = asgState.lessonsList.findIndex(lesson => parseInt(lesson.id) === parseInt(lessonId));
                const previousLesson = lessonIndex > 0 ? asgState.lessonsList[lessonIndex - 1] : null;

                const message = `
                    <div class="locked-lesson-overlay">
                        <div class="locked-content">
                            <i class="bi bi-lock-fill text-warning" style="font-size: 3rem;"></i>
                            <h3>Lesson Locked</h3>
                            <p>You must complete the previous lesson to access this content.</p>
                            ${previousLesson ? `
                                <div class="previous-lesson-info">
                                    <p><strong>Complete first:</strong> ${previousLesson.title}</p>
                                    <button class="btn" style="background: #0C1B40; color: white;" onclick="navigateToLesson(${previousLesson.id})">
                                        <i class="bi bi-arrow-left me-2"></i>Go to Previous Lesson
                                    </button>
                                </div>
                            ` : `
                                <button class="btn" style="background: #0C1B40; color: white;" onclick="findNextUnlockedLesson()">
                                    <i class="bi bi-arrow-right me-2"></i>Find Available Lesson
                                </button>
                            `}
                        </div>
                    </div>
                `;

                const contentArea = document.getElementById('lessonContentArea');
                if (contentArea) {
                    contentArea.innerHTML = message;
                }
            }

            /**
             * Find the next available lesson (Sequential Learning)
             */
            function findNextUnlockedLesson() {
                if (!asgState.courseProgress || !asgState.lessonsList) return null;

                const completedLessons = asgState.courseProgress.completed_lessons_list || [];

                // Find the first incomplete lesson
                for (let i = 0; i < asgState.lessonsList.length; i++) {
                    const lesson = asgState.lessonsList[i];
                    const lessonId = parseInt(lesson.id);

                    // If this lesson is not completed
                    if (!completedLessons.includes(lessonId)) {
                        // Check if it's unlocked (first lesson or previous is completed)
                        if (i === 0 || completedLessons.includes(parseInt(asgState.lessonsList[i - 1].id))) {
                          
                            navigateToLesson(lessonId);
                            return lessonId;
                        }
                    }
                }

                // All lessons completed
                showNotification('🎉 Congratulations! You have completed all lessons!', 'success');
                return null;
            }

            /**
             * Navegar a una lección específica
             */
            function navigateToLesson(lessonId, replaceHistory = false) {
                if (!lessonId) return;

                console.log('Navegando a lección:', lessonId);

                // Verificar si la lección está bloqueada
                if (isLessonLocked(lessonId)) {

                    showLockedLessonMessage(lessonId);
                    showNotification('Lesson is blocked, complete the previous lesson.', 'warning');
                    return;
                }

                const newUrl = `${window.location.pathname}?course=${ASG_CONFIG.COURSE_CODE}&lesson=${lessonId}`;

                // Use replaceState for initial navigation to avoid creating unnecessary history entries
                if (replaceHistory) {
                    window.history.replaceState({}, '', newUrl);
                } else {
                    window.history.pushState({}, '', newUrl);
                }

                // Update the lesson ID in config
                ASG_CONFIG.LESSON_ID = lessonId;

                loadLessonContent(lessonId);
            }

            // ===== GESTIÓN DE PROGRESO =====

            /**
             * Load user progress from server
             */
            async function loadUserProgress() {
                if (!ASG_CONFIG.COURSE_CODE) return;

                try {
                    console.log('🔄 Loading user progress for:', ASG_CONFIG.COURSE_CODE);

                    const result = await apiRequest('/my-courses');
                    console.log('📊 Progress response:', result);

                    if (result.success && result.data.courses) {
                        const courseProgress = result.data.courses.find(course =>
                            course.code_course === ASG_CONFIG.COURSE_CODE
                        );

                        if (courseProgress) {
                            console.log('✅ Course progress found:', courseProgress);
                            asgState.courseProgress = courseProgress;
                            updateProgressBar(courseProgress);
                            updateLessonsListUI(courseProgress);
                            return courseProgress;
                        } else {
                            console.log('⚠️ No progress found for course:', ASG_CONFIG.COURSE_CODE);
                            return createInitialProgress();
                        }
                    } else {
                        console.error('❌ Error in progress response:', result);
                        throw new Error(result.message || 'Error getting progress');
                    }

                } catch (error) {
                    console.error('❌ Error loading progress from server:', error);
                    console.log('🔄 Trying local storage fallback...');
                    return loadProgressFromLocalStorage();
                }
            }

            /**
             * Create initial progress structure
             */
            function createInitialProgress() {
                const initialProgress = {
                    code_course: ASG_CONFIG.COURSE_CODE,
                    completed_lessons: 0,
                    total_lessons: asgState.lessonsList.length,
                    progress_percentage: 0,
                    completed_lessons_list: [],
                    unlocked_lessons_list: [parseInt(asgState.lessonsList[0]?.id)] // Only first lesson unlocked
                };
                asgState.courseProgress = initialProgress;
                updateProgressBar(initialProgress);
                updateLessonsListUI(initialProgress);
                return initialProgress;
            }

            /**
             * Load progress from local storage as fallback
             */
            function loadProgressFromLocalStorage() {
                const storageKey = `asg_progress_${ASG_CONFIG.COURSE_CODE}`;
                const localProgress = JSON.parse(localStorage.getItem(storageKey) || '{}');

                const fallbackProgress = {
                    code_course: ASG_CONFIG.COURSE_CODE,
                    completed_lessons: localProgress.completed_lessons?.length || 0,
                    total_lessons: asgState.lessonsList.length,
                    progress_percentage: 0,
                    completed_lessons_list: localProgress.completed_lessons || [],
                    unlocked_lessons_list: [parseInt(asgState.lessonsList[0]?.id)]
                };

                // Calculate progress percentage
                if (fallbackProgress.total_lessons > 0) {
                    fallbackProgress.progress_percentage = Math.round(
                        (fallbackProgress.completed_lessons / fallbackProgress.total_lessons) * 100
                    );
                }

                console.log('💾 Using local storage progress:', fallbackProgress);
                asgState.courseProgress = fallbackProgress;
                updateProgressBar(fallbackProgress);
                updateLessonsListUI(fallbackProgress);
                return fallbackProgress;
            }

            /**
             * Mark lesson as completed con verificación robusta
             */
            async function markLessonComplete(lessonId) {
                if (!lessonId) {
                    lessonId = getCurrentLessonId();
                }

                if (!lessonId) {
                    showNotification('Could not identify current lesson', 'error');
                    return;
                }

                console.log('🎯 Marking lesson as complete:', lessonId);

                // VERIFICACIÓN ROBUSTA: Verificar acceso antes de marcar como completada
                const hasAccess = await verifyAccessBeforeLoad();
                if (!hasAccess) {
                    console.log('🚫 Access denied - cannot mark lesson as complete');
                    showNotification('Access denied. Please purchase the course to continue.', 'error');
                    return;
                }

                try {
                    // Try the primary API endpoint first
                    const url = `/lesson/${lessonId}/complete`;
                    console.log('📤 Sending POST to:', ASG_CONFIG.API_BASE + url);

                    const result = await apiRequest(url, {
                        method: 'POST',
                        headers: {
                            'X-WP-Nonce': document.querySelector('meta[name="wp-nonce"]')?.content || '',
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            course: ASG_CONFIG.COURSE_CODE,
                            lesson_id: parseInt(lessonId)
                        })
                    });

                    if (result.success) {
                        handleLessonCompletionSuccess(lessonId);
                        return;
                    } else {
                        throw new Error(result.message || 'Server error');
                    }

                } catch (error) {
                    console.error('❌ Primary endpoint failed:', error);

                    // Try fallback endpoint without nonce
                    try {
                        console.log('🔄 Trying fallback endpoint...');
                        const fallbackUrl = `/lesson/${lessonId}/complete-no-nonce`;

                        const fallbackResult = await apiRequest(fallbackUrl, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                course: ASG_CONFIG.COURSE_CODE,
                                lesson_id: parseInt(lessonId)
                            })
                        });

                        if (fallbackResult.success) {
                            handleLessonCompletionSuccess(lessonId);
                            return;
                        } else {
                            throw new Error(fallbackResult.message || 'Fallback error');
                        }

                    } catch (fallbackError) {
                        console.error('❌ Fallback endpoint failed:', fallbackError);

                        // Final fallback: Local storage completion
                        console.log('🔄 Using local storage fallback...');
                        handleLocalLessonCompletion(lessonId);
                    }
                }
            }

            /**
             * Handle successful lesson completion
             */
            function handleLessonCompletionSuccess(lessonId) {
                showNotification('🎉 Lección completada exitosamente!', 'success');

                // Update local state
                if (!asgState.courseProgress) {
                    asgState.courseProgress = {
                        completed_lessons_list: [],
                        progress_percentage: 0
                    };
                }

                // Add to completed lessons if not already there
                if (!asgState.courseProgress.completed_lessons_list.includes(parseInt(lessonId))) {
                    asgState.courseProgress.completed_lessons_list.push(parseInt(lessonId));
                }

                // Force refresh all lesson states and reload from server
                setTimeout(async () => {
                    console.log('🔄 Refrescando progreso desde servidor después de completar...');

                    // Reload progress from server to ensure sync
                    await loadUserProgress();

                    // Refresh UI with updated data
                    refreshLessonStates();

                    // REMOVER AUTO-NAVEGACIÓN AUTOMÁTICA
                    // Solo mostrar mensaje de éxito, no redirigir automáticamente
                    showNotification('✅ ¡Lección completada! Puedes continuar con la siguiente cuando quieras.', 'success');
                    
                    // Comentar la auto-navegación:
                    /*
                    const nextLesson = getNextLesson(lessonId);
                    if (nextLesson && asgState.courseProgress.progress_percentage < 100) {
                        setTimeout(() => {
                            console.log('🚀 Auto-navigating to next lesson:', nextLesson.title);
                            navigateToLesson(nextLesson.id);
                            setTimeout(() => {
                                scrollToLessonTitle();
                            }, 500);
                        }, 2000);
                    }
                    */

                }, 500);
            }

            /**
             * Handle lesson completion using local storage as fallback
             */
            function handleLocalLessonCompletion(lessonId) {
                console.log('💾 Using local storage for lesson completion');

                // Get or create local progress
                const storageKey = `asg_progress_${ASG_CONFIG.COURSE_CODE}`;
                let localProgress = JSON.parse(localStorage.getItem(storageKey) || '{}');

                if (!localProgress.completed_lessons) {
                    localProgress.completed_lessons = [];
                }

                // Add lesson if not already completed
                if (!localProgress.completed_lessons.includes(parseInt(lessonId))) {
                    localProgress.completed_lessons.push(parseInt(lessonId));
                    localStorage.setItem(storageKey, JSON.stringify(localProgress));
                }

                // Update global state
                if (!asgState.courseProgress) {
                    asgState.courseProgress = {
                        completed_lessons_list: [],
                        progress_percentage: 0
                    };
                }

                asgState.courseProgress.completed_lessons_list = localProgress.completed_lessons;
                const totalLessons = asgState.lessonsList.length;
                asgState.courseProgress.progress_percentage = Math.round((localProgress.completed_lessons.length / totalLessons) * 100);

              
                handleLessonCompletionSuccess(lessonId);
            }

            /**
             * Get next lesson in sequence
             */
            function getNextLesson(currentLessonId) {
                if (!asgState.lessonsList) return null;

                const currentIndex = asgState.lessonsList.findIndex(lesson =>
                    parseInt(lesson.id) === parseInt(currentLessonId)
                );

                if (currentIndex >= 0 && currentIndex < asgState.lessonsList.length - 1) {
                    return asgState.lessonsList[currentIndex + 1];
                }

                return null;
            }

            /**
             * Show course completion celebration modal with certificate
             */
            function showCourseCompletionModal() {
                // Create modal HTML
                const modalHTML = `
                    <div id="courseCompletionModal" class="completion-modal-overlay">
                        <div class="completion-modal-content">
                            <div class="completion-celebration">
                                <div class="confetti-container">
                                    <div class="confetti"></div>
                                    <div class="confetti"></div>
                                    <div class="confetti"></div>
                                    <div class="confetti"></div>
                                    <div class="confetti"></div>
                                    <div class="confetti"></div>
                                    <div class="confetti"></div>
                                    <div class="confetti"></div>
                                    <div class="confetti"></div>
                                    <div class="confetti"></div>
                                </div>

                                <div class="completion-header">
                                    <div class="completion-icon">🎉</div>
                                    <h2>Congratulations!</h2>
                                    <p>You have successfully completed the course</p>
                                </div>

                                <div class="certificate-container">
                                    <div class="certificate">
                                        <div class="certificate-border">
                                            <div class="certificate-content">
                                                <div class="certificate-header">
                                                    <h3>CERTIFICATE OF COMPLETION</h3>
                                                    <div class="certificate-logo">🏆</div>
                                                </div>

                                                <div class="certificate-body">
                                                    <p class="certificate-text">This is to certify that</p>
                                                    <h4 class="student-name" id="certificateStudentName">Student</h4>
                                                    <p class="certificate-text">has successfully completed the course</p>
                                                    <h4 class="course-name" id="certificateCourseName">Course Name</h4>
                                                    <p class="completion-date">Date of completion: <span id="certificateDate"></span></p>
                                                </div>

                                                <div class="certificate-footer">
                                                    <div class="signature-line">
                                                        <div class="signature">Ability Seminars Group</div>
                                                        <div class="signature-title">Certifying Institution</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="completion-actions">
                                    <button class="btn-download-certificate" onclick="downloadCertificate()">
                                        📄 Download Certificate
                                    </button>
                                    <button class="btn-share-achievement" onclick="shareAchievement()">
                                        🔗 Share Achievement
                                    </button>
                                    <button class="btn-close-modal" onclick="closeCourseCompletionModal(); window.location.href='/my-programs';">
                                        Continue
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // Add modal to page
                document.body.insertAdjacentHTML('beforeend', modalHTML);

                // Populate certificate data
                populateCertificateData();

                // Show modal with animation
                setTimeout(() => {
                    document.getElementById('courseCompletionModal').classList.add('show');
                }, 100);
            }

            /**
             * Populate certificate with user and course data
             */
            function populateCertificateData() {
                // Get current user info (you might need to adjust this based on your user system)
                const userName = ASG_CONFIG.USER_NAME || 'Student';
                const courseName = asgState.currentCourse?.name_course || 'Completed Course';
                const currentDate = new Date().toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                });

                // Populate certificate fields
                document.getElementById('certificateStudentName').textContent = userName;
                document.getElementById('certificateCourseName').textContent = courseName;
                document.getElementById('certificateDate').textContent = currentDate;
            }

            /**
             * Download certificate as image
             */
            function downloadCertificate() {
                const certificate = document.querySelector('.certificate');

                // Use html2canvas to convert certificate to image
                if (typeof html2canvas !== 'undefined') {
                    html2canvas(certificate, {
                        backgroundColor: '#ffffff',
                        scale: 2,
                        useCORS: true
                    }).then(canvas => {
                        const link = document.createElement('a');
                        link.download = `certificado-${asgState.currentCourse?.name_course || 'curso'}.png`;
                        link.href = canvas.toDataURL();
                        link.click();
                    });
                } else {
                    // Fallback: open certificate in new window for manual save
                    const printWindow = window.open('', '_blank');
                    printWindow.document.write(`
                        <html>
                            <head>
                                <title>Certificado de Finalización</title>
                                <style>
                                    body { margin: 0; padding: 20px; font-family: 'Outfit', sans-serif; }
                                    .certificate { max-width: 800px; margin: 0 auto; }
                                </style>
                            </head>
                            <body>
                                ${certificate.outerHTML}
                            </body>
                        </html>
                    `);
                    printWindow.document.close();
                }
            }

            /**
             * Share achievement on social media
             */
            function shareAchievement() {
                const courseName = asgState.currentCourse?.name_course || 'a course';
                const shareText = `I just completed the course "${courseName}" at Ability Seminars Group! 🎉🏆`;
                const shareUrl = window.location.href;

                if (navigator.share) {
                    // Use native sharing if available
                    navigator.share({
                        title: 'Certificate of Completion',
                        text: shareText,
                        url: shareUrl
                    });
                } else {
                    // Fallback: copy to clipboard
                    navigator.clipboard.writeText(`${shareText} ${shareUrl}`).then(() => {
                        showNotification('Text copied to clipboard! Share it on your social media.', 'success');
                    });
                }
            }

            /**
             * Close course completion modal
             */
            function closeCourseCompletionModal() {
                const modal = document.getElementById('courseCompletionModal');
                if (modal) {
                    modal.classList.remove('show');
                    setTimeout(() => {
                        modal.remove();
                    }, 300);
                }
            }

            /**
             * Scroll to lesson title for better user experience
             */
            function scrollToLessonTitle() {
                const lessonTitle = document.querySelector('.header-content');
                if (lessonTitle) {
                    lessonTitle.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start',
                        inline: 'nearest'
                    });
                    console.log('📍 Scrolled to lesson title');
                } else {
                    // Fallback: scroll to lesson header if lesson-title-main not found
                    const lessonHeader = document.querySelector('.header');
                    if (lessonHeader) {
                        lessonHeader.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start',
                            inline: 'nearest'
                        });
                        console.log('📍 Scrolled to lesson header (fallback)');
                    }
                }
            }

            /**
             * Load comments from backend and inject them into #commentsList
             */
            async function loadComments() {
                const lessonId = getCurrentLessonId();
                if (!lessonId) {
                    console.warn('No lesson ID available for comments');
                    return;
                }

                try {
                    console.log('📝 Loading comments for lesson:', lessonId);
                    const res = await fetch(`${ASG_CONFIG.API_BASE}/lessons/${lessonId}/comments`);
                    const data = await res.json();
                    const list = document.getElementById('commentsList');

                    if (data.success && Array.isArray(data.comments)) {
                        list.innerHTML = data.comments.map(c => `
                            <div class="list-group-item">
                                <strong>${c.author || 'Anonymous'}</strong>
                                <p class="mb-1" style="width:90%">${c.comment}</p>
                                <small class="text-muted">${c.date || ''}</small>
                            </div>
                        `).join('');
                        console.log('✅ Comments loaded:', data.comments.length);
                    } else {
                        list.innerHTML = `<div class="list-group-item text-muted">No comments yet.</div>`;
                    }
                } catch (e) {
                    console.error('❌ Error loading comments:', e);
                    const list = document.getElementById('commentsList');
                    if (list) {
                        list.innerHTML = `<div class="list-group-item text-danger">Error loading comments.</div>`;
                    }
                }
            }

            /**
             * Submit a new comment and reload the list if successful
             */
            async function submitComment(event) {
                event.preventDefault();
                const lessonId = getCurrentLessonId();
                const textEl = document.getElementById('commentText');

                if (!lessonId || !textEl) {
                    console.error('Missing required elements for comment submission');
                    return;
                }

                const comment = textEl.value.trim();
                if (!comment) return;

                try {
                    console.log('📝 Submitting comment for lesson:', lessonId);
                    const res = await fetch(`${ASG_CONFIG.API_BASE}/lessons/${lessonId}/comments`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-WP-Nonce': document.querySelector('meta[name="wp-nonce"]')?.content || ''
                        },
                        body: JSON.stringify({ comment })
                    });

                    const data = await res.json();
                    if (data.success) {
                        textEl.value = '';
                        loadComments();
                        console.log('✅ Comment submitted successfully');
                        showNotification('Comment posted successfully! 💬', 'success');
                    } else {
                        console.warn('Error submitting comment:', data);
                        showNotification('Error posting comment: ' + (data.message || 'Unknown error'), 'error');
                    }
                } catch (e) {
                    console.error('❌ Error submitting comment:', e);
                    showNotification('Error posting comment: ' + e.message, 'error');
                }
            }

            /**
             * Initialize comments system for current lesson
             */
            function initializeComments() {
                console.log('💬 Initializing comments system...');

                // Set up form event listener
                const form = document.getElementById('commentForm');
                if (form) {
                    form.addEventListener('submit', submitComment);
                }

                // Load comments for current lesson
                loadComments();
            }

            /**
             * Get previous lesson in sequence
             */
            function getPreviousLesson(currentLessonId) {
                if (!asgState.lessonsList) return null;

                const currentIndex = asgState.lessonsList.findIndex(lesson =>
                    parseInt(lesson.id) === parseInt(currentLessonId)
                );

                if (currentIndex > 0) {
                    return asgState.lessonsList[currentIndex - 1];
                }

                return null;
            }



            /**
             * Force refresh all lesson states after completion
             */
            function refreshLessonStates() {
                
                // Force re-render of lessons list
                if (asgState.courseProgress) {
                    updateLessonsListUI(asgState.courseProgress);
                }

                // Update navigation buttons
                updateNavigationButtons();

                // Update progress bar
                if (asgState.courseProgress) {
                    updateProgressBar(asgState.courseProgress);
                }

              }

            /**
             * ===== VERIFICACIÓN ROBUSTA DE ENROLLMENT (JavaScript) =====
             * Capa adicional de seguridad para verificar enrollment
             */
            async function checkEnrollmentRobust() {
                if (!ASG_CONFIG.COURSE_CODE) {
                    console.warn('🚫 No course code provided for enrollment check');
                    return false;
                }

                try {
                    console.log('🔍 Checking enrollment for course:', ASG_CONFIG.COURSE_CODE);

                    const result = await apiRequest(`/check-enrollment?course=${ASG_CONFIG.COURSE_CODE}`);
                    console.log('📊 Enrollment check result:', result);

                    if (result.success && result.enrolled) {
                        console.log('✅ User is enrolled in course');
                        return true;
                    } else {
                        console.log('❌ User is NOT enrolled in course');
                        return false;
                    }

                } catch (error) {
                    console.error('🚨 Error checking enrollment:', error);
                    // En caso de error, asumir que no está inscrito por seguridad
                    return false;
                }
            }

            /**
             * Verificación de acceso antes de cargar contenido
             */
            async function verifyAccessBeforeLoad() {
                console.log('🔐 Verifying access before loading content...');

                // Verificar si el usuario está logueado
                if (!ASG_CONFIG.USER_ID) {
                    console.log('🚫 User not logged in - blocking access');
                    logSecurityEvent('ACCESS_DENIED_NOT_LOGGED_IN', {
                        courseCode: ASG_CONFIG.COURSE_CODE,
                        attemptedAction: 'access_course_content'
                    });
                    blockAccessAndRedirectWithLogging('login', 'User not logged in');
                    return false;
                }

                // Verificar enrollment
                const isEnrolled = await checkEnrollmentRobust();
                if (!isEnrolled) {
                    console.log('🚫 User not enrolled - blocking access');
                    logSecurityEvent('ACCESS_DENIED_NOT_ENROLLED', {
                        courseCode: ASG_CONFIG.COURSE_CODE,
                        userId: ASG_CONFIG.USER_ID,
                        attemptedAction: 'access_course_content'
                    });
                    blockAccessAndRedirectWithLogging('purchase', 'User not enrolled in course');
                    return false;
                }

                console.log('✅ Access verified - user can proceed');
                logSecurityEvent('ACCESS_GRANTED', {
                    courseCode: ASG_CONFIG.COURSE_CODE,
                    userId: ASG_CONFIG.USER_ID,
                    verificationLevel: 'ROBUST'
                });
                return true;
            }

            /**
             * Bloquear acceso y redirigir según el tipo de problema
             */
            function blockAccessAndRedirect(type) {
                const contentArea = document.getElementById('lessonContentArea');
                let redirectUrl = '';
                let message = '';
                let buttonText = '';
                let buttonIcon = '';

                if (type === 'login') {
                    redirectUrl = `/wp-login.php?redirect_to=${encodeURIComponent(window.location.href)}`;
                    message = 'You need to sign in to access this course.';
                    buttonText = 'Sign In';
                    buttonIcon = 'box-arrow-in-right';
                } else if (type === 'purchase') {
                    redirectUrl = `/payment/?course=${encodeURIComponent(ASG_CONFIG.COURSE_CODE)}&price=10`;
                    message = 'You need to purchase this course to access the lessons.';
                    buttonText = 'Purchase Course';
                    buttonIcon = 'cart-plus';
                }

                if (contentArea) {
                    contentArea.innerHTML = `
                        <div class="access-blocked-overlay" style="
                            display: flex;
                            flex-direction: column;
                            align-items: center;
                            justify-content: center;
                            min-height: 400px;
                            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                            border-radius: 15px;
                            padding: 3rem;
                            text-align: center;
                        ">
                            <i class="bi bi-shield-x" style="font-size: 4rem; color: #dc3545; margin-bottom: 1.5rem;"></i>
                            <h3 style="color: var(--primary-blue); margin-bottom: 1rem;">Access Blocked</h3>
                            <p style="color: var(--text-light); font-size: 1.1rem; margin-bottom: 2rem;">${message}</p>
                            <button onclick="window.location.href='${redirectUrl}'" class="btn btn-lg" style="
                                background: var(--primary-blue);
                                color: white;
                                padding: 0.75rem 2rem;
                                border-radius: 25px;
                                border: none;
                                cursor: pointer;
                                transition: all 0.3s ease;
                            ">
                                <i class="bi bi-${buttonIcon} me-2"></i>${buttonText}
                            </button>
                            <small class="text-muted mt-3">
                                <i class="bi bi-shield-check me-1"></i>
                                Security verification by ASG Learning Platform
                            </small>
                        </div>
                    `;
                }

                // También bloquear navegación
                const navButtons = document.querySelectorAll('.nav-button, .completion-button');
                navButtons.forEach(button => {
                    button.disabled = true;
                    button.style.opacity = '0.5';
                    button.style.cursor = 'not-allowed';
                });

                // Mostrar notificación
                showNotification(message, 'error', 5000);
            }

            // Mantener función original para compatibilidad
            async function checkEnrollment() {
                return await checkEnrollmentRobust();
            }

            /**
             * ===== SISTEMA DE LOGGING DE SEGURIDAD =====
             */
            function logSecurityEvent(eventType, details) {
                const timestamp = new Date().toISOString();
                const logEntry = {
                    timestamp,
                    eventType,
                    courseCode: ASG_CONFIG.COURSE_CODE,
                    userId: ASG_CONFIG.USER_ID,
                    userAgent: navigator.userAgent,
                    url: window.location.href,
                    details
                };

                console.log(`🔒 SECURITY LOG [${eventType}]:`, logEntry);

                // Enviar log al servidor (opcional)
                try {
                    fetch(`${ASG_CONFIG.API_BASE}/security-log`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-WP-Nonce': document.querySelector('meta[name="wp-nonce"]')?.content || ''
                        },
                        body: JSON.stringify(logEntry)
                    }).catch(error => {
                        console.warn('Failed to send security log to server:', error);
                    });
                } catch (error) {
                    console.warn('Security logging error:', error);
                }
            }

            /**
             * Función mejorada de bloqueo con logging
             */
            function blockAccessAndRedirectWithLogging(type, reason) {
                // Log del evento de seguridad
                logSecurityEvent('ACCESS_BLOCKED', {
                    blockType: type,
                    reason: reason,
                    timestamp: new Date().toISOString()
                });

                // Llamar a la función original de bloqueo
                blockAccessAndRedirect(type);
            }

            // ===== GESTIÓN DE LECCIONES =====

            /**
             * Cargar datos del curso y lecciones con verificación robusta
             */
            async function loadCourseData() {
                if (!ASG_CONFIG.COURSE_CODE) {
                    console.error('🚫 No course code provided');
                    return;
                }

                try {
                    asgState.isLoading = true;
                    console.log('🔄 Loading course data for:', ASG_CONFIG.COURSE_CODE);

                    // VERIFICACIÓN ROBUSTA: Verificar acceso antes de cargar datos
                    const hasAccess = await verifyAccessBeforeLoad();
                    if (!hasAccess) {
                        console.log('🚫 Access denied - stopping course data load');
                        asgState.isLoading = false;
                        return;
                    }

                    // Usar el endpoint correcto que existe
                    const result = await apiRequest(`/courses/api/${ASG_CONFIG.COURSE_CODE}`);
                    console.log('📊 Course data response:', result);

                    if (result.success && result.data) {
                        // El endpoint devuelve un curso individual
                        const courseData = result.data;

                        asgState.currentCourse = courseData;
                        asgState.lessonsList = extractLessonsFromCourse(courseData);

                        // Actualizar título del curso en el header
                        const courseTitle = document.getElementById('courseTitle');
                        if (courseTitle && courseData.title) {
                            courseTitle.textContent = courseData.title;
                        }

                        // Log de módulos e imágenes
                        if (courseData.modules) {
                            courseData.modules.forEach((module, index) => {
                                console.log(`📁 Módulo ${index + 1}:`, {
                                    title: module.title_module,
                                    cover_img: module.cover_img,
                                    lessons_count: module.lessons ? module.lessons.length : 0
                                });
                            });
                        }

                        renderLessonsList();

                        // VERIFICACIÓN ROBUSTA: Doble verificación de enrollment
                        const isEnrolled = await checkEnrollmentRobust();
                        if (!isEnrolled) {
                            console.log('🚨 SECURITY ALERT: User bypassed initial verification');
                            blockAccessAndRedirect('purchase');
                            return;
                        }

                        // Cargar progreso del usuario
                        await loadUserProgress();

                        // Solo cargar lección específica si se proporciona en URL
                        if (ASG_CONFIG.LESSON_ID) {
                            loadLessonContent(ASG_CONFIG.LESSON_ID);
                        } else {
                            // No redirigir automáticamente, mostrar mensaje de selección
                            showLessonSelectionMessage();
                        }

                    } else {
                     
                        showError('No se pudo cargar el curso');
                    }
                } catch (error) {
                    showError('Error cargando datos del curso: ' + error.message);
                } finally {
                    asgState.isLoading = false;
                }
            }

            /**
             * Extraer lista de lecciones desde datos del curso
             */
            function extractLessonsFromCourse(courseData) {
                const lessons = [];


                if (courseData.modules && Array.isArray(courseData.modules)) {
                    courseData.modules.forEach((module, moduleIndex) => {
                        console.log(`Procesando módulo ${moduleIndex}:`, module);

                        if (module.lessons && Array.isArray(module.lessons)) {
                            module.lessons.forEach((lesson, lessonIndex) => {
                                console.log(`  Procesando lección ${lessonIndex}:`, lesson);

                                lessons.push({
                                    id: lesson.id_lesson || lesson.id,
                                    title: lesson.title_lesson || lesson.title || `Lección ${lessonIndex + 1}`,
                                    type: lesson.lesson_type || lesson.type_lesson || lesson.type || 'video',
                                    content: lesson.content_lesson || lesson.content || '',
                                    video_url: lesson.video_url || lesson.videoUrl || '',
                                    cover_img: lesson.cover_img || '',
                                    module_title: module.title_module || module.title || `Módulo ${moduleIndex + 1}`,
                                    module_cover_img: module.cover_img || '',
                                    is_preview: lesson.is_preview === '1' || lesson.is_preview === 1 || lesson.isPreview === true
                                });
                            });
                        } else {
                       
                        }
                    });
                } else {
                 
                }

               
                return lessons;
            }

            /**
             * Cargar contenido de una lección específica con verificación robusta
             */
            async function loadLessonContent(lessonId) {
                console.log('🔄 Loading lesson content for ID:', lessonId);

                // VERIFICACIÓN ROBUSTA: Verificar acceso antes de cargar lección
                const hasAccess = await verifyAccessBeforeLoad();
                if (!hasAccess) {
                    console.log('🚫 Access denied - stopping lesson content load');
                    return;
                }

                const lesson = asgState.lessonsList.find(l => l.id == lessonId);

                if (!lesson) {
                    console.error('❌ Lesson not found:', lessonId);
                    showError('Lesson not found');
                    return;
                }

                try {
                    // Normalizar tipo de lección antes de renderizar
                    if (!lesson.type && !lesson.type_lesson && !lesson.lesson_type) {
                        // Auto-detectar tipo basado en contenido
                        if (lesson.content) {
                            try {
                                const parsedContent = JSON.parse(lesson.content);
                                if (parsedContent.questions && Array.isArray(parsedContent.questions)) {
                                    lesson.type = 'quiz';
                                } else {
                                    lesson.type = lesson.video_url ? 'video' : 'text';
                                }
                            } catch (e) {
                                // No es JSON válido, determinar por video_url
                                lesson.type = lesson.video_url ? 'video' : 'text';
                            }
                        } else if (lesson.video_url) {
                            lesson.type = 'video';
                        } else {
                            // Sin contenido ni video, asumir text
                            lesson.type = 'text';
                        }
                    }

                    asgState.currentLesson = lesson;
                    updateLessonsListActive(lessonId);
                    renderLessonContent(lesson);
                } catch (error) {
                    console.error('Error cargando contenido de lección:', error);
                    showError('Error cargando contenido de la lección');
                }
            }

            // ===== GESTIÓN DE UI =====

            /**
             * Renderizar estructura de módulos y lecciones en el sidebar
             */
            function renderLessonsList() {
                const container = document.getElementById('modulesList');

                if (!container) {
                    console.log('ℹ️ Container modulesList no encontrado - usando diseño sin sidebar');
                    return;
                }

                if (!asgState.currentCourse || !asgState.currentCourse.modules) {
                    container.innerHTML = `
                        <div class="text-center py-4">
                            <i class="bi bi-book text-white" style="font-size: 2rem;"></i>
                            <p class="text-white mt-2">No modules available</p>
                            <button class="btn btn-sm btn-outline-primary" onclick="loadCourseData()">
                                <i class="bi bi-arrow-clockwise me-1"></i>Reload
                            </button>
                        </div>
                    `;
                    return;
                }

                console.log('Renderizando', asgState.currentCourse.modules.length, 'módulos');

                // Actualizar título del curso
                const courseTitle = document.getElementById('courseTitle');
                if (courseTitle) {
                    courseTitle.innerHTML = `
                        <i class="bi bi-mortarboard me-2"></i>
                        ${asgState.currentCourse.name_course || 'Curso'}
                    `;
                }

                // Renderizar módulos
                const modulesHtml = asgState.currentCourse.modules.map((module, moduleIndex) => {
                    const moduleId = `module-${moduleIndex}`;
                    const lessonsHtml = module.lessons ? module.lessons.map(lesson => {
                        const isActive = lesson.id_lesson == ASG_CONFIG.LESSON_ID;
                        const lessonType = lesson.type_lesson || lesson.lesson_type || 'video';
                        const icon = lessonType === 'video' ? 'play-circle' : 'question-circle';
                        const isPreview = lesson.is_preview === '1' || lesson.is_preview === 1;

                        return `
                            <div class="lesson-item ${isActive ? 'active' : ''} ${isPreview ? 'preview-lesson' : 'unlocked-lesson'}"
                                 onclick="navigateToLesson(${lesson.id_lesson})"
                                 data-lesson-id="${lesson.id_lesson}">
                                <div class="lesson-icon me-3">
                                    <i class="bi bi-${icon}"></i>
                                </div>
                                <div class="lesson-title flex-grow-1">${lesson.title_lesson}</div>
                                ${isPreview ? '<span class="badge bg-warning ms-2">Preview</span>' : ''}
                                <div class="lesson-status ms-2">
                                    <i class="bi bi-circle text-white"></i>
                                </div>
                            </div>
                        `;
                    }).join('') : '<p class="text-white p-3">You dont have any lessons in this module</p>';

                    return `
                        <div class="module-item">
                            <div class="module-header" onclick="toggleModule('${moduleId}')">
                                <h6 class="module-title">${module.title_module || `Module ${moduleIndex + 1}`}</h6>
                                <i class="bi bi-chevron-right module-chevron" id="chevron-${moduleId}"></i>
                            </div>
                            <div class="module-lessons" id="${moduleId}">
                                <div class="lessons-list">
                                    ${lessonsHtml}
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');

                container.innerHTML = modulesHtml;

                // Expandir el primer módulo por defecto
                setTimeout(() => {
                    const firstModule = document.getElementById('module-0');
                    const firstChevron = document.getElementById('chevron-module-0');
                    if (firstModule && firstChevron) {
                        firstModule.classList.add('show');
                        firstChevron.classList.add('rotated');
                    }
                }, 100);

                console.log('Estructura de módulos renderizada correctamente');
            }

            /**
             * Actualizar estado activo en lista de lecciones
             */
            function updateLessonsListActive(activeLessonId) {
                const items = document.querySelectorAll('.lesson-item');
                items.forEach(item => {
                    item.classList.remove('active');
                    if (item.dataset.lessonId == activeLessonId) {
                        item.classList.add('active');
                    }
                });
            }

            /**
             * Update lessons list UI with progress and sequential locking
             */
            function updateLessonsListUI(progressData) {
                if (!progressData) return;

                const completedLessons = progressData.completed_lessons_list || [];
                const allLessons = progressData.all_lessons_list || [];

                console.log('🎨 Updating lessons UI (Sequential Learning):', {
                    completed: completedLessons,
                    total: allLessons.length
                });

                // Check if modules list container exists
                const modulesList = document.getElementById('modulesList');
                if (!modulesList) {
                    console.log('ℹ️ Modules list container not found - UI update skipped');
                    return;
                }

                // If no lesson data, get from current structure
                if (allLessons.length === 0 && asgState.currentCourse && asgState.currentCourse.modules) {
                    asgState.currentCourse.modules.forEach(module => {
                        if (module.lessons) {
                            module.lessons.forEach(lesson => {
                                allLessons.push(parseInt(lesson.id_lesson));
                            });
                        }
                    });
                }

                // Use lessonsList for sequential order
                const orderedLessons = asgState.lessonsList || [];

                // Apply states to all lessons (Sequential Learning Logic)
                orderedLessons.forEach((lesson, index) => {
                    const lessonId = parseInt(lesson.id);
                    const lessonElement = document.querySelector(`[data-lesson-id="${lessonId}"]`);
                    if (!lessonElement) {
                        console.log(`⚠️ Lesson element not found for ID: ${lessonId}`);
                        return;
                    }

                    // Clear previous classes
                    lessonElement.classList.remove('completed', 'locked-lesson', 'unlocked-lesson');

                    // Remove previous badges
                    const existingBadge = lessonElement.querySelector('.lesson-badge');
                    if (existingBadge) existingBadge.remove();

                    const isCompleted = completedLessons.includes(lessonId);
                    const isFirstLesson = index === 0;
                    const isPreviousCompleted = index === 0 || completedLessons.includes(parseInt(orderedLessons[index - 1].id));
                    const isUnlocked = isFirstLesson || isPreviousCompleted;

                    // Apply lesson state
                    if (isCompleted) {
                        // ✅ COMPLETED LESSON
                        lessonElement.classList.add('completed');
                        const statusIcon = lessonElement.querySelector('.lesson-status i');
                        if (statusIcon) {
                            statusIcon.className = 'bi bi-check-circle-fill text-success';
                        }

                        // Visual completed style
                        lessonElement.style.opacity = '0.8';
                        lessonElement.style.textDecoration = 'line-through';

                        // Add completed badge
                        const badge = document.createElement('span');
                        badge.className = 'lesson-badge badge bg-success ms-2';
                        badge.innerHTML = '<i class="bi bi-check"></i> Completed';
                        lessonElement.appendChild(badge);

                    } else if (isUnlocked) {
                        // 🔓 UNLOCKED LESSON
                        lessonElement.classList.add('unlocked-lesson');
                        const statusIcon = lessonElement.querySelector('.lesson-status i');
                        if (statusIcon) {
                            statusIcon.className = 'bi bi-play-circle text-primary';
                        }

                        // Reset styles
                        lessonElement.style.opacity = '1';
                        lessonElement.style.textDecoration = 'none';
                        lessonElement.style.cursor = 'pointer';

                        // Add available badge
                        const badge = document.createElement('span');
                        badge.className = 'lesson-badge badge bg-primary ms-2';
                        badge.innerHTML = '<i class="bi bi-play"></i> Available';
                        lessonElement.appendChild(badge);

                    } else {
                        // 🔒 LOCKED LESSON
                        lessonElement.classList.add('locked-lesson');
                        const statusIcon = lessonElement.querySelector('.lesson-status i');
                        if (statusIcon) {
                            statusIcon.className = 'bi bi-lock-fill text-white';
                        }

                        // Locked styles
                        lessonElement.style.opacity = '0.5';
                        lessonElement.style.textDecoration = 'none';
                        lessonElement.style.cursor = 'not-allowed';

                        // Add locked badge
                        const badge = document.createElement('span');
                        badge.className = 'lesson-badge badge bg-secondary ms-2';
                        badge.innerHTML = '<i class="bi bi-lock"></i> Locked';
                        lessonElement.appendChild(badge);

                        // Disable click for locked lessons
                        lessonElement.onclick = (e) => {
                            e.preventDefault();
                            showNotification('🔒 Complete the previous lesson to unlock this one', 'warning');
                        };
                    }
                    });
			
			}

            /**
             * Renderizar contenido de lección
             */
            function renderLessonContent(lesson) {
                const container = document.getElementById('lessonContentArea');

                const isCompleted = asgState.courseProgress &&
                    asgState.courseProgress.completed_lessons_list &&
                    asgState.courseProgress.completed_lessons_list.includes(parseInt(lesson.id));

                // Detectar tipo de lección con múltiples fallbacks
                let lessonType = lesson.type || lesson.type_lesson || lesson.lesson_type || 'video';

                // Debug logging
                console.log('Renderizando lección:', {
                    id: lesson.id,
                    title: lesson.title,
                    type: lesson.type,
                    type_lesson: lesson.type_lesson,
                    lesson_type: lesson.lesson_type,
                    detected_type: lessonType,
                    has_video_url: !!lesson.video_url,
                    content_preview: lesson.content ? lesson.content.substring(0, 100) : 'No content'
                });

                // Si no hay video_url pero hay contenido, determinar si es quiz o text
                if (lessonType === 'video' && !lesson.video_url && lesson.content) {
                    try {
                        const parsedContent = JSON.parse(lesson.content);
                        if (parsedContent.questions && Array.isArray(parsedContent.questions)) {
                            lessonType = 'quiz';
                        } else {
                            lessonType = 'text';
                        }
                    } catch (e) {
                        lessonType = 'text';
                    }
                }

                // Actualizar información de la lección en el header
                updateLessonInfo(lesson);

                let contentHtml = '';

                // Lesson header
                const typeConfig = {
                    video: { icon: 'play-fill', label: 'Video Lesson' },
                    quiz: { icon: 'question-circle-fill', label: 'Interactive Quiz' },
                    text: { icon: 'file-text-fill', label: 'Text Lesson' }
                };

                const currentType = typeConfig[lessonType] || typeConfig.text;

                contentHtml += `
                    <div class="lesson-header">
                        <div class="lesson-type-badge lesson-type-${lessonType}">
                            <i class="bi bi-${currentType.icon} me-1"></i>
                            ${currentType.label}
                        </div>
                        <h1 class="lesson-title-main">${lesson.title}</h1>
                        <p class="text-muted mb-0">Module: ${lesson.module_title}</p>
                    </div>
                `;

                // Contenido según tipo de lección detectado
                if (lessonType === 'video') {
                    contentHtml += renderVideoContent(lesson);
                } else if (lessonType === 'quiz') {
                    contentHtml += renderQuizContent(lesson);
                } else if (lessonType === 'text') {
                    contentHtml += renderTextContent(lesson);
                } else {
                    // Fallback para tipos desconocidos
                    contentHtml += renderTextContent(lesson);
                }



                container.innerHTML = contentHtml;

           

                // Actualizar navegación con los botones existentes del diseño
                updateNavigationSection(lesson, isCompleted);
                updateNavigationButtons();

                // Initialize comments system for this lesson
                setTimeout(() => {
                    initializeComments();
                }, 500); // Small delay to ensure content is fully loaded
            }

            /**
             * Actualizar información de la lección en el header
             */
            function updateLessonInfo(lesson) {
                // Actualizar número de lección
                const lessonNumber = document.getElementById('lessonNumber');
                if (lessonNumber) {
                    const navInfo = getNavigationInfo();
                    lessonNumber.textContent = `Lesson N.${navInfo.currentIndex + 1}`;
                }

                // Actualizar título de lección
                const lessonTitle = document.getElementById('lessonTitle');
                if (lessonTitle) {
                    lessonTitle.textContent = lesson.title;
                }

                // Actualizar información del módulo
                const moduleInfo = document.getElementById('moduleInfo');
                if (moduleInfo) {
                    moduleInfo.textContent = lesson.module_title ? `Module ${lesson.module_title}` : 'Module 1.1';
                }
            }

            /**
             * Renderizar contenido de video para el nuevo diseño
             */
            function renderVideoContentNew(lesson) {
                let contentHtml = `
                    <div class="lesson-header">
                        <h1 class="lesson-title">${lesson.title}</h1>
                        <p class="lesson-description">${lesson.description || 'Video lesson content'}</p>
                    </div>
                `;

                if (lesson.video_url) {
                    contentHtml += `
                        <div class="video-container">
                            <iframe src="${lesson.video_url}"
                                    allowfullscreen>
                            </iframe>
                        </div>
                    `;
                }

                if (lesson.content) {
                    contentHtml += `
                        <div class="content-text">
                            ${lesson.content}
                        </div>
                    `;
                }

                return contentHtml;
            }

            /**
             * Renderizar contenido de texto para el nuevo diseño
             */
            function renderTextContentNew(lesson) {
                // Obtener imagen de la lección
                let lessonImage = lesson.cover_img;
                if (!lessonImage && lesson.module_cover_img) {
                    lessonImage = lesson.module_cover_img;
                }
                if (!lessonImage && asgState.currentCourse) {
                    lessonImage = asgState.currentCourse.cover_img;
                }

                let contentHtml = `
                    <div class="lesson-header">
                        <h1 class="lesson-title">Understanding The Basics of Marketing</h1>
                        <p class="lesson-description">Marketing is the discipline of identifying market needs and wants, then developing and offering a product that satisfies them, culminating in a successful transaction and, crucially, the loyalty of a satisfied customer.</p>
                    </div>
                `;

                // Imagen de ejemplo (como en el diseño)
                contentHtml += `
                    <div class="content-image">
                        <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDUwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI1MDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjRjhGOUZBIiBzdHJva2U9IiNFOUVDRUYiLz4KPHN2ZyB4PSI1MCIgeT0iNTAiIHdpZHRoPSI0MDAiIGhlaWdodD0iMjAwIj4KICA8cGF0aCBkPSJNNTAgMjAwTDEwMCAxNTBMMTUwIDEwMEwyMDAgMTUwTDI1MCA1MEwzMDAgMTAwTDM1MCA3NUw0MDAgMTI1IiBzdHJva2U9IiMwQzFCNDEiIHN0cm9rZS13aWR0aD0iMyIgZmlsbD0ibm9uZSIvPgogIDx0ZXh0IHg9IjIwMCIgeT0iMTAwIiBmb250LWZhbWlseT0iT3V0Zml0IiBmb250LXNpemU9IjQ4IiBmb250LXdlaWdodD0iYm9sZCIgZmlsbD0iIzBDMUI0MSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+RU1QPC90ZXh0Pgo8L3N2Zz4KPC9zdmc+" alt="Marketing Diagram" />
                    </div>
                `;

                // Contenido de texto estructurado como en el diseño
                contentHtml += `
                    <div class="content-section">
                        <div class="content-text">
                            <p>Personal Marketing applies this same fundamental principle to professional development. In this context, the professional is the "product," and the employer is the "customer." The process requires you to:</p>
                        </div>

                        <div class="highlight-box">
                            <h4>Identify the Employer's Need:</h4>
                            <p>Thoroughly understand the company's requirements, challenges, and objectives.</p>

                            <h4>Provide the Solution:</h4>
                            <p>Possess or develop the necessary skills to meet those needs.</p>

                            <h4>Communicate Your Value:</h4>
                            <p>Convince the employer that you are the right person to deliver that solution, securing not only the job but also your employer's full satisfaction upon seeing the results.</p>
                        </div>

                        <div class="content-text">
                            <p>To execute an effective personal marketing strategy, a clear understanding of your most important asset—yourself—is essential. Analyze your skills, define your exact value, and determine how you can directly contribute to the company's profitability and success.</p>
                        </div>
                    </div>
                `;

                return contentHtml;
            }

            /**
             * Renderizar contenido de quiz para el nuevo diseño
             */
            function renderQuizContentNew(lesson) {
                let contentHtml = `
                    <div class="lesson-header">
                        <h1 class="lesson-title">${lesson.title}</h1>
                        <p class="lesson-description">Interactive quiz to test your knowledge</p>
                    </div>
                `;

                try {
                    const quizData = JSON.parse(lesson.content);
                    if (quizData.questions && Array.isArray(quizData.questions)) {
                        contentHtml += `
                            <div class="quiz-container">
                                <div class="quiz-question">
                                    <h3>${quizData.questions[0].question}</h3>
                                </div>
                                <div class="quiz-options">
                        `;

                        quizData.questions[0].options.forEach((option, index) => {
                            contentHtml += `
                                <div class="quiz-option" onclick="selectQuizOption(this, ${index})">
                                    ${option}
                                </div>
                            `;
                        });

                        contentHtml += `
                                </div>
                            </div>
                        `;
                    }
                } catch (e) {
                    contentHtml += `<div class="content-text"><p>Quiz content could not be loaded.</p></div>`;
                }

                return contentHtml;
            }

            /**
             * Actualizar sección de navegación
             */
            function updateNavigationSection(lesson, isCompleted) {
                const navInfo = getNavigationInfo();

                // Actualizar contador de lecciones
                const lessonCounter = document.getElementById('lessonCounter');
                if (lessonCounter) {
                    lessonCounter.textContent = `${navInfo.currentIndex + 1} of ${navInfo.totalLessons} Lessons`;
                }

                // Actualizar botón anterior
                const btnPrevLesson = document.getElementById('btnPrevLesson');
                if (btnPrevLesson) {
                    btnPrevLesson.disabled = !navInfo.hasPrevious;
                    btnPrevLesson.onclick = () => navigateToPrevious();

                    // Actualizar texto del botón
                    if (navInfo.hasPrevious) {
                        btnPrevLesson.innerHTML = `
                            <i class="bi bi-arrow-left"></i>
                            Previous Lesson
                        `;
                        btnPrevLesson.title = navInfo.previousLesson?.title || 'Previous Lesson';
                    } else {
                        btnPrevLesson.innerHTML = `
                            <i class="bi bi-arrow-left"></i>
                            Previous Lesson
                        `;
                        btnPrevLesson.title = 'This is the first lesson';
                    }
                }

                // Actualizar botón siguiente
                const btnNextLesson = document.getElementById('btnNextLesson');
                if (btnNextLesson) {
                    const canNavigateNext = navInfo.hasNext && !isLessonLocked(navInfo.nextLesson?.id);
                    btnNextLesson.disabled = !canNavigateNext;
                    btnNextLesson.onclick = () => navigateToNext();

                    // Actualizar texto del botón
                    if (navInfo.hasNext) {
                        if (canNavigateNext) {
                            btnNextLesson.innerHTML = `
                                Next Lesson
                                <i class="bi bi-arrow-right"></i>
                            `;
                            btnNextLesson.title = navInfo.nextLesson?.title || 'Next Lesson';
                        } else {
                            btnNextLesson.innerHTML = `
                                Next Lesson
                                <i class="bi bi-arrow-right"></i>
                            `;
                            btnNextLesson.title = 'Complete this lesson to continue';
                        }
                    } else {
                        btnNextLesson.innerHTML = `
                            Course Complete
                            <i class="bi bi-check-circle"></i>
                        `;
                        btnNextLesson.title = 'You have completed all lessons!';
                    }
                }

                // Actualizar botón de completar
                const btnMarkComplete = document.getElementById('btnMarkComplete');
                console.log('🔘 Updating complete button:', {
                    buttonExists: !!btnMarkComplete,
                    isCompleted: isCompleted,
                    lessonId: lesson.id
                });

                if (btnMarkComplete) {
                    if (isCompleted) {
                        btnMarkComplete.innerHTML = '<i class="bi bi-check-circle-fill"></i> Completed';
                        btnMarkComplete.disabled = true;
                        btnMarkComplete.classList.add('completed');
                        btnMarkComplete.style.background = '#6c757d';
                        btnMarkComplete.style.color = 'white';
                        btnMarkComplete.onclick = null;
                        console.log('✅ Button set to completed state');
                    } else {
                        btnMarkComplete.innerHTML = '<i class="bi bi-check-circle"></i> Mark as Complete';
                        btnMarkComplete.disabled = false;
                        btnMarkComplete.classList.remove('completed');
                        btnMarkComplete.style.background = '#28a745';
                        btnMarkComplete.style.color = 'white';
                        btnMarkComplete.onclick = () => {
                            console.log('🎯 Complete button clicked for lesson:', lesson.id);
                            markLessonComplete(lesson.id);
                        };
                        console.log('🔘 Button set to active state');
                    }
                } else {
                    console.error('❌ btnMarkComplete element not found in DOM');
                }
            }

            /**
             * Manejar selección de opción de quiz
             */
            function selectQuizOption(element, optionIndex) {
                // Remove previous selection and reset styles
                document.querySelectorAll('.quiz-option').forEach(opt => {
                    opt.classList.remove('selected');
                    opt.style.borderColor = '';
                    opt.style.backgroundColor = '';
                    opt.style.color = '';
                });
                
                // Remove any existing error messages
                document.querySelectorAll('.alert-danger, .alert-success').forEach(alert => alert.remove());
                
                // Select new option
                element.classList.add('selected');
            }

         

            /**
             * Inicializar eventos del nuevo diseño
             */
            function initializeNewDesignEvents() {
                // Botón de contenido del curso
                const btnCourseContent = document.getElementById('btnCourseContent');
                if (btnCourseContent) {
                    btnCourseContent.onclick = () => {
                        showCourseContentModal();
                    };
                }

              
            
            }

            /**
             * Renderizar contenido de texto con imagen (función original mantenida para compatibilidad)
             */
            function renderTextContent(lesson) {
                let contentHtml = '';

                // Verificar si hay imagen de lección específica
                let lessonImage = lesson.cover_img;

                // Si no hay imagen de lección, usar imagen del módulo o curso
                if (!lessonImage) {
                    // Primero intentar usar la imagen del módulo desde los datos de la lección
                    if (lesson.module_cover_img) {
                        lessonImage = lesson.module_cover_img;
                    }
                    // Si no, buscar en la estructura del curso
                    else if (asgState.currentCourse && asgState.currentCourse.modules) {
                        const lessonModule = asgState.currentCourse.modules.find(module =>
                            module.lessons && module.lessons.some(l => l.id_lesson == lesson.id)
                        );

                        if (lessonModule && lessonModule.cover_img) {
                            lessonImage = lessonModule.cover_img;
                        }
                    }

                    // Si no hay imagen del módulo, usar imagen del curso
                    if (!lessonImage && asgState.currentCourse) {
                        lessonImage = asgState.currentCourse.cover_img;
                    }
                }

                // Fallback a imagen placeholder si no hay ninguna imagen
                if (!lessonImage) {
                    lessonImage = 'https://via.placeholder.com/800x400/1e88e5/ffffff?text=Lección+de+Texto';
                }

                console.log('Renderizando contenido de texto:', {
                    lessonId: lesson.id,
                    lessonTitle: lesson.title,
                    hasContent: !!lesson.content,
                    images: {
                        lesson_cover: lesson.cover_img,
                        module_cover: lesson.module_cover_img,
                        course_cover: asgState.currentCourse?.cover_img,
                        final_image: lessonImage
                    },
                    imageSource: lessonImage?.includes('placeholder') ? 'placeholder' :
                                lessonImage === lesson.cover_img ? 'lesson' :
                                lessonImage === lesson.module_cover_img ? 'module' :
                                lessonImage === asgState.currentCourse?.cover_img ? 'course' : 'unknown'
                });

                contentHtml += '<div class="text-lesson-container">';

                // Procesar contenido de texto para separar primer párrafo
                let firstParagraph = '';
                let remainingContent = '';

                if (lesson.content) {
                    // Obtener solo el texto sin HTML para buscar el primer punto
                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = lesson.content;
                    const textContent = tempDiv.textContent || tempDiv.innerText || '';

                    // Buscar el primer punto en el texto
                    const firstDotIndex = textContent.indexOf('.');

                    if (firstDotIndex !== -1) {
                        // Encontró un punto, obtener el texto hasta el primer punto
                        const firstSentenceText = textContent.substring(0, firstDotIndex + 1).trim();
                        const remainingText = textContent.substring(firstDotIndex + 1).trim();

                        // Crear el primer párrafo con el texto hasta el primer punto
                        firstParagraph = `<p>${firstSentenceText}</p>`;

                        // Si hay texto restante, crear el contenido restante
                        if (remainingText) {
                            // Buscar en el HTML original donde termina la primera oración
                            const originalHtml = lesson.content;
                            const firstDotInHtml = originalHtml.indexOf('.');

                            if (firstDotInHtml !== -1) {
                                // Obtener el HTML después del primer punto
                                let afterDotHtml = originalHtml.substring(firstDotInHtml + 1).trim();

                                // Si el contenido restante no está envuelto en párrafos, envolverlo
                                if (afterDotHtml && !afterDotHtml.startsWith('<')) {
                                    afterDotHtml = `<p>${afterDotHtml}</p>`;
                                }

                                remainingContent = afterDotHtml;
                            } else {
                                // Fallback: usar el texto restante como párrafo
                                remainingContent = `<p>${remainingText}</p>`;
                            }
                        }
                    } else {
                        // No encontró punto, usar todo el contenido como primer párrafo
                        firstParagraph = `<p>${textContent}</p>`;
                        remainingContent = '';
                    }
                }

                // Layout con imagen y primer párrafo lado a lado
                if (lessonImage && firstParagraph) {
                    const isLessonImage = lessonImage === lesson.cover_img;
                    const isModuleImage = lessonImage === lesson.module_cover_img;
                    const isCourseImage = lessonImage === asgState.currentCourse?.cover_img;
                    const isPlaceholder = lessonImage.includes('placeholder');

                    let captionText = 'Module Image';
                    if (isLessonImage) {
                        captionText = 'Lesson Image';
                    } else if (isModuleImage) {
                        captionText = 'Module Image';
                    } else if (isCourseImage) {
                        captionText = 'Course Image';
                    } else if (isPlaceholder) {
                        captionText = 'Example Image';
                    }

                    contentHtml += `
                        <div class="image-text-layout">
                            <div class="lesson-image-container">
                                <img src="${lessonImage}"
                                     alt="${lesson.title}"
                                     class="lesson-image"
                                     onerror="this.style.display='none'">
                                <div class="image-caption">
                                    <small class="text-white">
                                        ${captionText}
                                    </small>
                                </div>
                            </div>
                            <div class="first-paragraph">
                                ${firstParagraph}
                            </div>
                        </div>
                    `;

                    // Mostrar el resto del contenido debajo
                    if (remainingContent.trim()) {
                        contentHtml += `
                            <div class="remaining-content">
                                ${remainingContent}
                            </div>
                        `;
                    }
                } else if (lessonImage) {
                    // Solo imagen, sin contenido
                    const isLessonImage = lessonImage === lesson.cover_img;
                    const isModuleImage = lessonImage === lesson.module_cover_img;
                    const isCourseImage = lessonImage === asgState.currentCourse?.cover_img;
                     const isPlaceholder = lessonImage.includes('placeholder');

                    let captionText = 'Imagen del módulo';
                    if (isLessonImage) {
                        captionText = 'Imagen de la lección';
                    } else if (isModuleImage) {
                        captionText = 'Imagen del módulo';
                   } else if (isCourseImage) {
                        captionText = 'Imagen del curso';
                   } else if (isPlaceholder) {
                        captionText = 'Imagen de ejemplo';
                    }

                    contentHtml += `
                        <div class="lesson-image-container">
                            <img src="${lessonImage}"
                                 alt="${lesson.title}"
                                 class="lesson-image"
                                 onerror="this.style.display='none'">
                            <div class="image-caption">
                                <small class="text-white">
                                    ${captionText}
                                </small>
                            </div>
                        </div>
                    `;
                } else if (lesson.content) {
                    // Solo contenido, sin imagen
                    contentHtml += `
                        <div class="lesson-text-content">
                            ${lesson.content}
                        </div>
                    `;
                } else {
                    // Ni imagen ni contenido
                    contentHtml += `
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                           Content not available.
                        </div>
                    `;
                }

                contentHtml += '</div>';


                  


                return contentHtml;
            }

            /**
             * Renderizar contenido de video
             */
            function renderVideoContent(lesson) {
                if (!lesson.video_url) {
                    // Si no hay video pero hay contenido, mostrar el contenido
                    if (lesson.content) {
                        return `
                            <div class="lesson-content-text">
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle me-2"></i>
                                   This lessons has content of text.
                                </div>
                                <div class="content-body">
                                    ${lesson.content}
                                </div>
                            </div>
                        `;
                    }
                    return `
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            Video not found it.
                            <br><small class="text-white">Contact us.</small>
                        </div>
                    `;
                }

                // Convertir URL de YouTube a formato embed si es necesario
                let embedUrl = lesson.video_url;
                if (lesson.video_url.includes('youtube.com/watch')) {
                    const videoId = lesson.video_url.split('v=')[1]?.split('&')[0];
                    if (videoId) {
                        embedUrl = `https://www.youtube.com/embed/${videoId}`;
                    }
                } else if (lesson.video_url.includes('youtu.be/')) {
                    const videoId = lesson.video_url.split('youtu.be/')[1]?.split('?')[0];
                    if (videoId) {
                        embedUrl = `https://www.youtube.com/embed/${videoId}`;
                    }
                }

                return `
                    <div class="video-container">
                        <iframe src="${embedUrl}"
                                allowfullscreen
                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture">
                        </iframe>
                    </div>
                    ${lesson.content ? `<div class="lesson-description">${lesson.content}</div>` : ''}
                `;
            }

            /**
             * Crear quiz de ejemplo si no hay datos
             */
            function createSampleQuiz(lesson) {
                return {
                    title: lesson.title + ' - Quiz',
                    description: 'Test your knowledge with this interactive quiz.',
                    passing_score: 70,
                    questions: [
                        {
                            id: 1,
                            type: 'multiple_choice',
                            question: 'What did you learn in this lesson?',
                            options: [
                                'Important concepts and key points',
                                'Nothing significant',
                                'Basic information only',
                                'Advanced techniques'
                            ],
                            correct_answer: 0,
                            allow_multiple: false
                        },
                        {
                            id: 2,
                            type: 'true_false',
                            question: 'Did you understand the main concepts of this lesson?',
                            correct_answer: true
                        }
                    ]
                };
            }

            /**
             * Renderizar contenido de quiz
             */
            function renderQuizContent(lesson) {
                // Parse quiz data from content_lesson
                let quizData;
                try {
                    quizData = lesson.content ? JSON.parse(lesson.content) : null;
                } catch (e) {
                    quizData = null;
                }

                // If no quiz data, create a sample quiz
                if (!quizData || !quizData.questions) {
                    quizData = createSampleQuiz(lesson);
                }

                // Store quiz data globally
                asgState.currentQuiz = quizData;
                asgState.quizAnswers = [];
                asgState.currentQuestionIndex = 0;
                asgState.quizScore = 0;

                return `
                    <div class="quiz-lesson-container">
                        <div class="quiz-intro" id="quizIntro">
                            <div class="quiz-intro-content">
                                <h3><i class="bi bi-trophy me-2"></i>${quizData.title || lesson.title}</h3>
                                <p class="quiz-description">${quizData.description || 'Test your knowledge with this interactive quiz.'}</p>

                                <div class="quiz-stats">
                                    <div class="stat-item">
                                        <i class="bi bi-question-circle me-2"></i>
                                        <span>${quizData.questions.length} Questions</span>
                                    </div>
                                    <div class="stat-item">
                                        <i class="bi bi-clock me-2"></i>
                                        <span>10 minutes</span>
                                    </div>
                                    <div class="stat-item">
                                        <i class="bi bi-target me-2"></i>
                                        <span>${quizData.passing_score || 70}% to pass</span>
                                    </div>
                                </div>

                                <button class="btn btn-lg start-quiz-btn" style="background: #0C1B40; color: white;" onclick="startQuiz()">
                                    <i class="bi bi-play-circle me-2"></i>Start Quiz
                                </button>
                            </div>
                        </div>

                        <div class="quiz-content" id="quizContent" style="display: none;">
                            <!-- Quiz questions will be rendered here -->
                        </div>

                        <div class="quiz-results" id="quizResults" style="display: none;">
                            <!-- Quiz results will be shown here -->
                        </div>
                    </div>
                `;
            }

            /**
             * Crear quiz de ejemplo si no hay datos
             */
            
            /**
             * Actualizar botón de completar lección
             */
            function updateLessonCompleteButton(lessonId, isCompleted) {
                const button = document.querySelector('.btn-complete');
                if (button) {
                    if (isCompleted) {
                        button.innerHTML = '<i class="bi bi-check-circle-fill me-2"></i>Complete';
                        button.classList.add('disabled');
                        button.disabled = true;
                    } else {
                        button.innerHTML = '<i class="bi bi-check-circle me-2"></i>Mark as Completed';
                        button.classList.remove('disabled');
                        button.disabled = false;
                    }
                }
            }

            /**
             * Toggle de módulos (expandir/colapsar)
             */
            function toggleModule(moduleId) {
                const module = document.getElementById(moduleId);
                const chevron = document.getElementById(`chevron-${moduleId}`);

                if (!module || !chevron) return;

                if (module.classList.contains('show')) {
                    // Colapsar módulo
                    module.classList.remove('show');
                    chevron.classList.remove('rotated');
                } else {
                    // Expandir módulo (opcional: colapsar otros)
                    // Colapsar otros módulos
                    document.querySelectorAll('.module-lessons.show').forEach(openModule => {
                        if (openModule.id !== moduleId) {
                            openModule.classList.remove('show');
                            const otherChevron = document.getElementById(`chevron-${openModule.id}`);
                            if (otherChevron) {
                                otherChevron.classList.remove('rotated');
                            }
                        }
                    });

                    // Expandir módulo actual
                    module.classList.add('show');
                    chevron.classList.add('rotated');
                }
            }

            /**
             * Configurar navegación móvil
             */
            function setupMobileNavigation() {
                const sidebarToggle = document.getElementById('sidebarToggle');
                const sidebar = document.getElementById('lessonSidebar');
                const layout = document.querySelector('.lesson-layout');

                if (sidebarToggle && sidebar && layout) {
                    sidebarToggle.addEventListener('click', function(e) {
                        e.stopPropagation();
                        sidebar.classList.toggle('show');
                        layout.classList.toggle('sidebar-open');
                    });

                    // Cerrar sidebar al hacer clic fuera en móvil
                    document.addEventListener('click', function(e) {
                        if (window.innerWidth <= 992) {
                            if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                                sidebar.classList.remove('show');
                                layout.classList.remove('sidebar-open');
                            }
                        }
                    });

                    // Cerrar sidebar al navegar a una lección en móvil
                    sidebar.addEventListener('click', function(e) {
                        if (e.target.closest('.lesson-item') && window.innerWidth <= 992) {
                            setTimeout(() => {
                                sidebar.classList.remove('show');
                                layout.classList.remove('sidebar-open');
                            }, 300);
                        }
                    });
                }
            }

            // ===== NAVEGACIÓN =====

            /**
             * Obtener lista ordenada de todas las lecciones
             */
            function getAllLessonsOrdered() {
                const allLessons = [];

                if (asgState.currentCourse && asgState.currentCourse.modules) {
                    asgState.currentCourse.modules.forEach(module => {
                        if (module.lessons) {
                            module.lessons.forEach(lesson => {
                                allLessons.push({
                                    id: parseInt(lesson.id_lesson),
                                    title: lesson.title_lesson,
                                    module: module.title_module
                                });
                            });
                        }
                    });
                }

                return allLessons.sort((a, b) => a.id - b.id);
            }

            /**
             * Encontrar índice de lección actual
             */
            function getCurrentLessonIndex() {
                const allLessons = getAllLessonsOrdered();
                const currentLessonId = parseInt(ASG_CONFIG.LESSON_ID);

                return allLessons.findIndex(lesson => lesson.id === currentLessonId);
            }

            /**
             * Navigate to previous lesson
             */
            function navigateToPrevious() {
                const allLessons = getAllLessonsOrdered();
                const currentIndex = getCurrentLessonIndex();

                if (currentIndex <= 0) {
                    showNotification('This is the first lesson', 'info');
                    return;
                }

                const previousLesson = allLessons[currentIndex - 1];

                // Previous lessons are always accessible
               console.log('📖 Navigating to previous lesson:', previousLesson.title);
                navigateToLesson(previousLesson.id);
            }

            /**
             * Debug function to show current navigation state
             */
            function debugNavigationState() {
                const navInfo = getNavigationInfo();
                const completedLessons = asgState.courseProgress?.completed_lessons_list || [];

                console.log('🐛 NAVIGATION DEBUG STATE:', {
                    currentLesson: navInfo.currentLesson,
                    nextLesson: navInfo.nextLesson,
                    hasNext: navInfo.hasNext,
                    completedLessons: completedLessons,
                    isNextLocked: navInfo.nextLesson ? isLessonLocked(navInfo.nextLesson.id) : 'N/A',
                    courseProgress: asgState.courseProgress
                });

                return navInfo;
            }

            /**
             * Navigate to next lesson (Sequential Learning)
             */
            async function navigateToNext() {
                const allLessons = getAllLessonsOrdered();
                const currentIndex = getCurrentLessonIndex();

                if (currentIndex >= allLessons.length - 1) {
                    showNotification('🎉 This is the last lesson! Congratulations!', 'success');
                    return;
                }

                const nextLesson = allLessons[currentIndex + 1];
                const currentLesson = allLessons[currentIndex];

                // Show current progress state BEFORE refresh
                console.log('📋 BEFORE REFRESH - Current Progress State:', {
                    courseProgress: asgState.courseProgress,
                    completedLessons: asgState.courseProgress?.completed_lessons_list || [],
                    currentLessonId: currentLesson.id,
                    nextLessonId: nextLesson.id,
                    isNextLocked: isLessonLocked(nextLesson.id)
                });

                // Debug current state
                debugNavigationState();

                // Force refresh progress before checking lock status
                console.log('🔄 Refreshing progress before navigation...');
                await loadUserProgress();

                // Check if next lesson is unlocked after refresh
                const isNextLockedAfterRefresh = isLessonLocked(nextLesson.id);
                console.log('🔍 Next lesson lock status after refresh:', {
                    nextLessonId: nextLesson.id,
                    isLocked: isNextLockedAfterRefresh,
                    completedLessons: asgState.courseProgress?.completed_lessons_list || []
                });

                if (isNextLockedAfterRefresh) {
                    // Check if current lesson is completed
                    const completedLessons = asgState.courseProgress?.completed_lessons_list || [];
                    const isCurrentCompleted = completedLessons.includes(parseInt(currentLesson.id));

                    if (!isCurrentCompleted) {
                        showNotification('⚠️ Complete this lesson first to unlock the next one', 'warning');
                        // Highlight the complete button
                        const completeBtn = document.querySelector('.btn-complete, #btnMarkComplete');
                        if (completeBtn && !completeBtn.disabled) {
                            completeBtn.style.animation = 'pulse 1s infinite';
                            completeBtn.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            setTimeout(() => {
                                completeBtn.style.animation = '';
                            }, 3000);
                        }
                    } else {
                        showNotification('🔒 Next lesson is still locked. There might be a synchronization issue.', 'warning');
                        console.warn('⚠️ Current lesson is completed but next is still locked:', {
                            currentLessonId: currentLesson.id,
                            nextLessonId: nextLesson.id,
                            completedLessons: completedLessons
                        });
                    }
                    return;
                }

                console.log('📖 Navigating to next lesson:', nextLesson.title);

                // Ensure we have a clean lesson ID (remove any extra characters)
                let cleanLessonId = nextLesson.id;
                if (typeof cleanLessonId === 'string' && cleanLessonId.includes(':')) {
                    cleanLessonId = cleanLessonId.split(':')[0];
                }
                cleanLessonId = parseInt(cleanLessonId);

                console.log('🔧 Clean lesson ID:', cleanLessonId);
                navigateToLesson(cleanLessonId);
            }

            /**
             * Obtener información de navegación
             */
            function getNavigationInfo() {
                const allLessons = getAllLessonsOrdered();
                const currentIndex = getCurrentLessonIndex();

                return {
                    currentIndex: currentIndex,
                    totalLessons: allLessons.length,
                    hasPrevious: currentIndex > 0,
                    hasNext: currentIndex < allLessons.length - 1,
                    previousLesson: currentIndex > 0 ? allLessons[currentIndex - 1] : null,
                    nextLesson: currentIndex < allLessons.length - 1 ? allLessons[currentIndex + 1] : null,
                    currentLesson: currentIndex >= 0 ? allLessons[currentIndex] : null
                };
            }

            /**
             * Actualizar estado de botones de navegación (VERSIÓN UNIFICADA)
             */
            function updateNavigationButtons() {
                const navInfo = getNavigationInfo();
                const prevBtn = document.getElementById('prevBtn');
                const nextBtn = document.getElementById('nextBtn');

                if (prevBtn) {
                    const canNavigatePrev = navInfo.hasPrevious;
                    prevBtn.disabled = !canNavigatePrev;
                    prevBtn.style.opacity = !canNavigatePrev ? '0.5' : '1';
                    prevBtn.title = !navInfo.hasPrevious ? 'First lesson' : navInfo.previousLesson?.title;
                }

                if (nextBtn) {
                    const canNavigateNext = navInfo.hasNext && !isLessonLocked(navInfo.nextLesson?.id);
                    nextBtn.disabled = !canNavigateNext;
                    nextBtn.style.opacity = !canNavigateNext ? '0.5' : '1';
                    nextBtn.title = !navInfo.hasNext ? 'Last lesson' :
                                   !canNavigateNext ? 'Complete this lesson to continue' :
                                   navInfo.nextLesson?.title;
                }

                console.log('🔄 Navigation buttons updated:', {
                    hasPrevious: navInfo.hasPrevious,
                    hasNext: navInfo.hasNext,
                    nextLessonId: navInfo.nextLesson?.id,
                    isNextLocked: navInfo.nextLesson ? isLessonLocked(navInfo.nextLesson.id) : 'N/A'
                });
            }

            // ===== FUNCIONES AUXILIARES =====

            /**
             * Mostrar mensaje de error
             */
            function showError(message) {
                const container = document.getElementById('lessonContentArea');
                container.innerHTML = `
                    <div class="text-center py-5">
                        <i class="bi bi-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                        <h3 class="mt-3">Error</h3>
                        <p class="text-white">${message}</p>
                        <button class="btn" style="background: #0C1B40; color: white;" onclick="loadCourseData()">
                            <i class="bi bi-arrow-clockwise me-2"></i>Retry
                        </button>
                    </div>
                `;
            }

            /**
             * Iniciar quiz
             */
            function startQuiz() {
                if (!asgState.currentQuiz || !asgState.currentQuiz.questions) {
                    showNotification('No hay datos de quiz disponibles', 'error');
                    return;
                }

                // Reset quiz state
                asgState.quizAnswers = [];
                asgState.currentQuestionIndex = 0;
                asgState.quizScore = 0;

                // Hide intro and show quiz content
                document.getElementById('quizIntro').style.display = 'none';
                document.getElementById('quizContent').style.display = 'block';
                document.getElementById('quizResults').style.display = 'none';

                // Render first question
                renderQuizQuestion();
            }

            /**
             * Renderizar pregunta actual del quiz
             */
            function renderQuizQuestion() {
                const question = asgState.currentQuiz.questions[asgState.currentQuestionIndex];
                const totalQuestions = asgState.currentQuiz.questions.length;
                const progress = ((asgState.currentQuestionIndex + 1) / totalQuestions) * 100;

                const quizContent = document.getElementById('quizContent');

                let optionsHtml = '';
                let instructionText = '';

                if (question.type === 'multiple_choice') {
                    instructionText = question.allow_multiple ? 'Select all that apply:' : 'Select one option:';
                    const inputType = question.allow_multiple ? 'checkbox' : 'radio';

                    optionsHtml = question.options.map((option, index) => `
                        <div class="quiz-option" onclick="selectQuizOption(${index}, ${question.allow_multiple || false})">
                            <input type="${inputType}" name="question_${question.id}" value="${index}" style="pointer-events: none;">
                            <span class="option-text">${option}</span>
                        </div>
                    `).join('');
                } else if (question.type === 'true_false') {
                    instructionText = 'Select True or False:';
                    optionsHtml = `
                        <div class="quiz-option" onclick="selectQuizOption(true, false)">
                            <input type="radio" name="question_${question.id}" value="true" style="pointer-events: none;">
                            <span class="option-text">True</span>
                        </div>
                        <div class="quiz-option" onclick="selectQuizOption(false, false)">
                            <input type="radio" name="question_${question.id}" value="false" style="pointer-events: none;">
                            <span class="option-text">False</span>
                        </div>
                    `;
                } else if (question.type === 'text_field') {
                    instructionText = 'Type your answer (any answer is valid):';
                    optionsHtml = `
                        <div class="text-field-answer">
                            <input type="text" class="form-control" id="text_field_answer"
                                   placeholder="Type your answer here..."
                                   style="padding: 12px; font-size: 16px; border: 2px solid #ddd; border-radius: 8px;">
                        </div>
                    `;
                } else if (question.type === 'subjective') {
                    instructionText = 'Write your answer:';
                    optionsHtml = `
                        <div class="subjective-answer">
                            <textarea class="form-control" id="subjective_answer" rows="4"
                                      placeholder="Type your answer here..."></textarea>
                        </div>
                    `;
                }

                quizContent.innerHTML = `
                    <div class="quiz-header">
                        <div class="quiz-progress">
                            <div class="progress-bar" style="width: ${progress}%"></div>
                        </div>
                        <div class="question-counter">
                            Question ${asgState.currentQuestionIndex + 1} of ${totalQuestions}
                        </div>
                    </div>

                    <div class="quiz-question-content">
                        <h4 class="question-text">${question.question}</h4>
                        <p class="instruction-text">${instructionText}</p>

                        <div class="quiz-options">
                            ${optionsHtml}
                        </div>
                    </div>

                    <div class="quiz-navigation">
                        <button class="btn btn-outline-secondary" onclick="previousQuestion()"
                                ${asgState.currentQuestionIndex === 0 ? 'disabled' : ''}>
                            <i class="bi bi-arrow-left me-2"></i>Previous
                        </button>

                        <button class="btn" style="background: #0C1B40; color: white;" onclick="nextQuestion()" id="nextQuestionBtn">
                            ${asgState.currentQuestionIndex === totalQuestions - 1 ? 'Finish Quiz' : 'Next'}
                            <i class="bi bi-arrow-right ms-2"></i>
                        </button>
                    </div>
                `;

                // Restore previous answer if exists
                const previousAnswer = asgState.quizAnswers[asgState.currentQuestionIndex];
                if (previousAnswer !== undefined) {
                    restorePreviousAnswer(previousAnswer, question);
                }

                // Scroll to center the quiz question
                setTimeout(() => {
                    const questionElement = document.querySelector('.quiz-question-content');
                    if (questionElement) {
                        questionElement.scrollIntoView({ 
                            behavior: 'smooth', 
                            block: 'center' 
                        });
                    }
                }, 100);
            }

            // ===== SISTEMA DE MONITOREO CONTINUO =====

            /**
             * Monitoreo continuo de acceso (verificación cada 5 minutos)
             */
            function startAccessMonitoring() {
                console.log('🔍 Starting continuous access monitoring...');

                setInterval(async () => {
                    console.log('🔄 Performing periodic access check...');

                    const hasAccess = await verifyAccessBeforeLoad();
                    if (!hasAccess) {
                        console.log('🚨 SECURITY ALERT: Access lost during session');
                        // El usuario perdió acceso durante la sesión
                        showNotification('Your access to this course has expired. Please refresh the page.', 'warning', 10000);
                    }
                }, 5 * 60 * 1000); // Cada 5 minutos
            }

            /**
             * Verificación adicional en eventos de ventana
             */
            function setupSecurityEventListeners() {
                // Verificar cuando la ventana vuelve a tener foco
                window.addEventListener('focus', async () => {
                    console.log('🔍 Window focused - checking access...');
                    const hasAccess = await verifyAccessBeforeLoad();
                    if (!hasAccess) {
                        console.log('🚨 Access lost - blocking content');
                    }
                });

                // Verificar antes de navegar a otra lección
                window.addEventListener('beforeunload', () => {
                    console.log('🔄 Page unloading - access monitoring stopped');
                });
            }

            // ===== INICIALIZACIÓN =====

            /**
             * Inicializar la aplicación cuando el DOM esté listo con Verificación Robusta
             */
            document.addEventListener('DOMContentLoaded', async function() {
                console.log('🚀 ASG Lessons System initialized with ROBUST SECURITY');
                console.log('🔐 Security Level: MAXIMUM');
                console.log('URL actual:', window.location.href);
                console.log('Configuración final:', ASG_CONFIG);

                // Verificar configuración básica
                if (!ASG_CONFIG.COURSE_CODE) {
                    console.error('COURSE_CODE no definido después de fallbacks');
                    console.log('Parámetros URL disponibles:', new URLSearchParams(window.location.search));
                    showError('Código de curso no especificado en la URL');
                    return;
                }

                try {
                    // VERIFICACIÓN INICIAL ROBUSTA
                    console.log('🔐 Performing initial security verification...');

                    // Inicializar eventos del nuevo diseño
                    initializeNewDesignEvents();

                    // Configurar listeners de seguridad
                    setupSecurityEventListeners();

                    // Actualizar información del usuario
                    await updateUserInfo();

                    // Cargar datos del curso (incluye verificación robusta automática)
                    console.log('🔄 Iniciando carga de datos del curso con verificación robusta...');
                    await loadCourseData();

                    // Configurar navegación del navegador con verificación
                    window.addEventListener('popstate', async function() {
                        const lessonId = getCurrentLessonId();
                        if (lessonId) {
                            // Verificar acceso antes de cargar nueva lección
                            const hasAccess = await verifyAccessBeforeLoad();
                            if (hasAccess) {
                                loadLessonContent(lessonId);
                            }
                        }
                    });

                    // Iniciar monitoreo continuo de seguridad
                    startAccessMonitoring();

                    console.log('✅ Sistema inicializado correctamente con verificación robusta');

                } catch (error) {
                    console.error('❌ Error durante inicialización con seguridad robusta:', error);
                    showNotification('Error loading course with security verification', 'error');
                }
            });

            /**
             * Actualizar información del usuario en el header
             */
            async function updateUserInfo() {
                const username = document.getElementById('username');
                if (username && ASG_CONFIG.USER_ID) {
                    try {
                        // Obtener información del usuario desde WordPress
                        const response = await fetch(`${ASG_CONFIG.API_BASE.replace('/asg/v1', '')}/wp/v2/users/${ASG_CONFIG.USER_ID}`, {
                            headers: {
                                'X-WP-Nonce': document.querySelector('meta[name="wp-nonce"]')?.content || ''
                            }
                        });

                        if (response.ok) {
                            const userData = await response.json();
                            const displayName = userData.name || userData.display_name || userData.username || 'User';
                            username.textContent = displayName;
                            console.log('✅ Username updated to:', displayName);
                        } else {
                            // Fallback: usar información de PHP si está disponible
                            username.textContent = '<?php echo wp_get_current_user()->display_name ?: "User"; ?>';
                        }
                    } catch (error) {
                        console.error('❌ Error loading user info:', error);
                        // Fallback: usar información de PHP
                        username.textContent = '<?php echo wp_get_current_user()->display_name ?: "User"; ?>';
                    }
                }
            }

            /**
             * Seleccionar opción de quiz
             */
            function selectQuizOption(value, isMultiple = false) {
                if (isMultiple) {
                    // Handle multiple selection (checkboxes)
                    const checkbox = event.currentTarget.querySelector('input[type="checkbox"]');
                    checkbox.checked = !checkbox.checked;

                    // Update visual selection
                    if (checkbox.checked) {
                        event.currentTarget.classList.add('selected');
                    } else {
                        event.currentTarget.classList.remove('selected');
                    }

                    // Update answers array
                    let currentAnswers = asgState.quizAnswers[asgState.currentQuestionIndex] || [];
                    if (checkbox.checked) {
                        if (!currentAnswers.includes(value)) {
                            currentAnswers.push(value);
                        }
                    } else {
                        currentAnswers = currentAnswers.filter(answer => answer !== value);
                    }
                    asgState.quizAnswers[asgState.currentQuestionIndex] = currentAnswers;
                } else {
                    // Handle single selection (radio buttons)
                    const radio = event.currentTarget.querySelector('input[type="radio"]');
                    radio.checked = true;

                    // Remove selection from other options
                    document.querySelectorAll('.quiz-option').forEach(option => {
                        option.classList.remove('selected');
                    });

                    // Add selection to current option
                    event.currentTarget.classList.add('selected');

                    // Update answer
                    asgState.quizAnswers[asgState.currentQuestionIndex] = value;
                }
            }

            /**
             * Navegar a pregunta siguiente
             */
            function nextQuestion() {
                const currentQuestion = asgState.currentQuiz.questions[asgState.currentQuestionIndex];
                
                // Save current answer
                saveCurrentAnswer();
                
                // Get user's answer
                const userAnswer = asgState.quizAnswers[asgState.currentQuestionIndex];
                
                // Validate answer if question has correct answer defined
                if (currentQuestion.correct_answer !== undefined) {
                    const isCorrect = validateQuestionAnswer(currentQuestion, userAnswer);
                    
                    if (!isCorrect) {
                        // Show incorrect answer feedback
                        showIncorrectAnswerFeedback();
                        return; // Don't proceed to next question
                    } else {
                        // Show correct answer feedback briefly
                        showCorrectAnswerFeedback();
                    }
                }

                if (asgState.currentQuestionIndex < asgState.currentQuiz.questions.length - 1) {
                    asgState.currentQuestionIndex++;
                    renderQuizQuestion();
                } else {
                    finishQuiz();
                }
            }

            /**
             * Navegar a pregunta anterior
             */
            function previousQuestion() {
                if (asgState.currentQuestionIndex > 0) {
                    asgState.currentQuestionIndex--;
                    renderQuizQuestion();
                }
            }

            /**
             * Restaurar respuesta anterior
             */
            function restorePreviousAnswer(previousAnswer, question) {
                if (question.type === 'multiple_choice' && question.allow_multiple && Array.isArray(previousAnswer)) {
                    // Restore multiple selections
                    previousAnswer.forEach(value => {
                        const option = document.querySelector(`input[value="${value}"]`);
                        if (option) {
                            option.checked = true;
                            option.closest('.quiz-option').classList.add('selected');
                        }
                    });
                } else if (question.type === 'subjective') {
                    // Restore subjective answer
                    const textarea = document.getElementById('subjective_answer');
                    if (textarea && previousAnswer) {
                        textarea.value = previousAnswer;
                    }
                } else if (question.type === 'text_field') {
                    // Restore text field answer
                    const textInput = document.getElementById('text_field_answer');
                    if (textInput && previousAnswer) {
                        textInput.value = previousAnswer;
                    }
                } else {
                    // Restore single selection
                    const option = document.querySelector(`input[value="${previousAnswer}"]`);
                    if (option) {
                        option.checked = true;
                        option.closest('.quiz-option').classList.add('selected');
                    }
                }
            }

            // Hacer funciones disponibles globalmente para onclick
            window.navigateToLesson = navigateToLesson;
            window.navigateToPrevious = navigateToPrevious;
            window.navigateToNext = navigateToNext;
            window.markLessonComplete = markLessonComplete;
            window.startQuiz = startQuiz;
            window.loadCourseData = loadCourseData;
            window.toggleModule = toggleModule;

            // Debug functions
            window.asgDebug = {
                state: () => asgState,
                config: () => ASG_CONFIG,
                testCompletion: (lessonId) => markLessonComplete(lessonId),
                clearLocalStorage: () => {
                    const key = `asg_progress_${ASG_CONFIG.COURSE_CODE}`;
                    localStorage.removeItem(key);
                    console.log('Local storage cleared for course:', ASG_CONFIG.COURSE_CODE);
                },
                debugNavigation: () => debugNavigationState(),
                forceRefreshProgress: async () => {
                    console.log('🔄 Force refreshing progress...');
                    await loadUserProgress();
                    refreshLessonStates();
                    console.log('✅ Progress refreshed');
                },
                checkLessonLock: (lessonId) => {
                    const isLocked = isLessonLocked(lessonId);
                    console.log(`Lesson ${lessonId} is ${isLocked ? 'LOCKED' : 'UNLOCKED'}`);
                    return isLocked;
                },
                simulateCompletion: (lessonId) => {
                    if (!asgState.courseProgress) {
                        asgState.courseProgress = { completed_lessons_list: [] };
                    }
                    if (!asgState.courseProgress.completed_lessons_list.includes(parseInt(lessonId))) {
                        asgState.courseProgress.completed_lessons_list.push(parseInt(lessonId));
                        refreshLessonStates();
                        console.log(`✅ Simulated completion for lesson ${lessonId}`);
                    }
                }
            };
            /**
             * Finalizar quiz y mostrar resultados
             */
            async function finishQuiz() {
                try {
                    console.log('Finalizando quiz...');

                    // Save current answer for subjective questions
                    const currentQuestion = asgState.currentQuiz.questions[asgState.currentQuestionIndex];
                    if (currentQuestion.type === 'subjective') {
                        const textarea = document.getElementById('subjective_answer');
                        if (textarea) {
                            asgState.quizAnswers[asgState.currentQuestionIndex] = textarea.value;
                        }
                    }

                    // Calculate score
                    const scoreData = calculateQuizScore();
                    const passed = scoreData.score >= (asgState.currentQuiz.passing_score || 70);

                    // Show results
                    showQuizResults(scoreData, passed);

                    // If passed, mark lesson as complete
                    if (passed) {
                        setTimeout(() => {
                            markLessonComplete();
                        }, 2000);
                    }

                } catch (error) {
                    console.error('Error finishing quiz:', error);
                    showNotification('Error al finalizar el quiz', 'error');
                }
            }

            /**
             * Calcular puntuación del quiz
             */
            function calculateQuizScore() {
                let correctAnswers = 0;
                let totalQuestions = asgState.currentQuiz.questions.length;
                let subjectiveAnswers = 0;
                let totalPoints = 0;
                let earnedPoints = 0;

                asgState.currentQuiz.questions.forEach((question, index) => {
                    const userAnswer = asgState.quizAnswers[index];
                    const questionPoints = question.points || 25;
                    totalPoints += questionPoints;

                    if (question.type === 'subjective') {
                        // Subjective questions are auto-passed if answered
                        if (userAnswer && userAnswer.trim().length > 0) {
                            correctAnswers++;
                            earnedPoints += questionPoints;
                            subjectiveAnswers++;
                        }
                    } else if (question.type === 'multiple_choice' && question.allow_multiple) {
                        // Multiple selection questions
                        const correctAnswerSet = new Set(question.correct_answer);
                        const userAnswerSet = new Set(Array.isArray(userAnswer) ? userAnswer : []);

                        if (correctAnswerSet.size === userAnswerSet.size &&
                            [...correctAnswerSet].every(answer => userAnswerSet.has(answer))) {
                            correctAnswers++;
                            earnedPoints += questionPoints;
                        }
                    } else {
                        // Single selection questions
                        if (Array.isArray(question.correct_answer)) {
                            if (question.correct_answer.includes(userAnswer)) {
                                correctAnswers++;
                                earnedPoints += questionPoints;
                            }
                        } else {
                            if (question.correct_answer === userAnswer) {
                                correctAnswers++;
                                earnedPoints += questionPoints;
                            }
                        }
                    }
                });

                const score = totalPoints > 0 ? Math.round((earnedPoints / totalPoints) * 100) : 0;

                return {
                    score: score,
                    correct_answers: correctAnswers,
                    total_questions: totalQuestions,
                    subjective_answers: subjectiveAnswers,
                    earned_points: earnedPoints,
                    total_points: totalPoints
                };
            }

            /**
             * Mostrar resultados del quiz
             */
            function showQuizResults(scoreData, passed) {
                const passingScore = asgState.currentQuiz.passing_score || 70;

                document.getElementById('quizContent').style.display = 'none';
                const resultsContainer = document.getElementById('quizResults');
                resultsContainer.style.display = 'block';

                resultsContainer.innerHTML = `
                    <div class="quiz-results-container">
                        <div class="results-header ${passed ? 'passed' : 'failed'}">
                            <div class="score-circle">
                                <div class="score-number">${scoreData.score}%</div>
                            </div>
                            <h3>${passed ? '🎉 Congratulations!' : '😔 Try Again'}</h3>
                            <p>${passed ? 'You passed the quiz!' : `You need ${passingScore}% to pass.`}</p>
                        </div>

                        <div class="results-details">
                            <div class="detail-item">
                                <span class="label">Correct Answers:</span>
                                <span class="value">${scoreData.correct_answers}/${scoreData.total_questions}</span>
                            </div>
                            <div class="detail-item">
                                <span class="label">Score:</span>
                                <span class="value">${scoreData.score}%</span>
                            </div>
                            <div class="detail-item">
                                <span class="label">Passing Score:</span>
                                <span class="value">${passingScore}%</span>
                            </div>
                            ${scoreData.subjective_answers > 0 ? `
                                <div class="detail-item">
                                    <span class="label">Subjective Questions:</span>
                                    <span class="value">${scoreData.subjective_answers}</span>
                                </div>
                            ` : ''}
                        </div>

                        <div class="results-actions">
                            ${!passed ? `
                                <button class="btn btn-warning" onclick="retakeQuiz()">
                                    <i class="bi bi-arrow-clockwise me-2"></i>Retake Quiz
                                </button>
                            ` : ''}
                            <button class="btn" style="background: #0C1B40; color: white;" onclick="continueToNextLesson()">
                                <i class="bi bi-arrow-right me-2"></i>Continue
                            </button>
                        </div>
                    </div>
                `;
            }

            /**
             * Reintentar quiz
             */
            function retakeQuiz() {
                document.getElementById('quizResults').style.display = 'none';
                startQuiz();
            }

            /**
             * Continuar a siguiente lección
             */
            function continueToNextLesson() {
                navigateToNext();
            }

            /**
             * Navegar a pregunta anterior del quiz
             */
            function previousQuestion() {
                if (asgState.currentQuestionIndex > 0) {
                    // Save current answer before moving
                    saveCurrentAnswer();
                    asgState.currentQuestionIndex--;
                    renderQuizQuestion();
                }
            }

            /**
             * Navegar a siguiente pregunta del quiz
             */
            function nextQuestion() {
                // Save current answer
                saveCurrentAnswer();

                if (asgState.currentQuestionIndex < asgState.currentQuiz.questions.length - 1) {
                    asgState.currentQuestionIndex++;
                    renderQuizQuestion();
                } else {
                    // Last question, finish quiz
                    finishQuiz();
                }
            }

            /**
             * Guardar respuesta actual
             */
            function saveCurrentAnswer() {
                const currentQuestion = asgState.currentQuiz.questions[asgState.currentQuestionIndex];

                if (currentQuestion.type === 'multiple_choice') {
                    if (currentQuestion.allow_multiple) {
                        // Multiple selection
                        const selectedOptions = [];
                        document.querySelectorAll('input[type="checkbox"]:checked').forEach(checkbox => {
                            selectedOptions.push(parseInt(checkbox.value));
                        });
                        asgState.quizAnswers[asgState.currentQuestionIndex] = selectedOptions;
                    } else {
                        // Single selection
                        const selectedOption = document.querySelector('input[type="radio"]:checked');
                        if (selectedOption) {
                            asgState.quizAnswers[asgState.currentQuestionIndex] = parseInt(selectedOption.value);
                        }
                    }
                } else if (currentQuestion.type === 'true_false') {
                    const selectedOption = document.querySelector('input[type="radio"]:checked');
                    if (selectedOption) {
                        asgState.quizAnswers[asgState.currentQuestionIndex] = selectedOption.value === 'true';
                    }
                } else if (currentQuestion.type === 'subjective') {
                    const textarea = document.getElementById('subjective_answer');
                    if (textarea) {
                        asgState.quizAnswers[asgState.currentQuestionIndex] = textarea.value;
                    }
                }
            }

            /**
             * Restaurar respuesta anterior
             */
            function restorePreviousAnswer(previousAnswer, question) {
                if (question.type === 'multiple_choice' && question.allow_multiple && Array.isArray(previousAnswer)) {
                    // Restore multiple selections
                    previousAnswer.forEach(value => {
                        const option = document.querySelector(`input[value="${value}"]`);
                        if (option) {
                            option.checked = true;
                            option.closest('.quiz-option').classList.add('selected');
                        }
                    });
                } else if (question.type === 'subjective') {
                    // Restore subjective answer
                    const textarea = document.getElementById('subjective_answer');
                    if (textarea && previousAnswer) {
                        textarea.value = previousAnswer;
                    }
                } else {
                    // Restore single selection
                    const option = document.querySelector(`input[value="${previousAnswer}"]`);
                    if (option) {
                        option.checked = true;
                        option.closest('.quiz-option').classList.add('selected');
                    }
                }
            }



            window.selectQuizOption = selectQuizOption;

          
      

            window.nextQuestion = nextQuestion;
            window.previousQuestion = previousQuestion;
            window.finishQuiz = finishQuiz;
            window.retakeQuiz = retakeQuiz;
            window.continueToNextLesson = continueToNextLesson;
            window.findNextUnlockedLesson = findNextUnlockedLesson;
            window.showCourseContentModal = showCourseContentModal;
            window.closeCourseContentModal = closeCourseContentModal;

            // Certificate modal functions
            window.showCourseCompletionModal = showCourseCompletionModal;
            window.populateCertificateData = populateCertificateData;
            window.downloadCertificate = downloadCertificate;
            window.shareAchievement = shareAchievement;
            window.closeCourseCompletionModal = closeCourseCompletionModal;

            // Scroll function
            window.scrollToLessonTitle = scrollToLessonTitle;

            // Comments functions
            window.loadComments = loadComments;
            window.submitComment = submitComment;
            window.initializeComments = initializeComments;

            /**
             * Mostrar modal con contenido del curso
             */
            function showCourseContentModal() {
                const modal = document.getElementById('courseContentModal');
                const modalBody = document.getElementById('courseContentBody');

                if (modal && modalBody) {
                    modal.style.display = 'flex';
                    renderCourseContent(modalBody);
                }
            }

            /**
             * Cerrar modal de contenido del curso
             */
            function closeCourseContentModal() {
                const modal = document.getElementById('courseContentModal');
                if (modal) {
                    modal.style.display = 'none';
                }
            }

            // Cerrar modal al hacer clic fuera de él
            document.addEventListener('click', function(e) {
                const modal = document.getElementById('courseContentModal');
                if (e.target === modal) {
                    closeCourseContentModal();
                }
            });

            /**
             * Renderizar contenido del curso en el modal
             */
            function renderCourseContent(container) {
                if (!asgState.currentCourse || !asgState.currentCourse.modules) {
                    container.innerHTML = `
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            Course content not available
                        </div>
                    `;
                    return;
                }

                const completedLessons = asgState.courseProgress?.completed_lessons_list || [];

                let contentHtml = '';

                asgState.currentCourse.modules.forEach((module, moduleIndex) => {
                    const moduleId = `modal-module-${moduleIndex}`;

                    // Debug module data
                    console.log('📁 Module data:', {
                        index: moduleIndex,
                        module: module,
                        title: module.title || module.module_title || module.name,
                        lessons: module.lessons
                    });

                    let lessonsHtml = '';
                    if (module.lessons && module.lessons.length > 0) {
                        lessonsHtml = module.lessons.map((lesson, lessonIndex) => {
                            // Try multiple possible field names for lesson ID and title
                            const lessonId = parseInt(lesson.id_lesson || lesson.id || lesson.lesson_id);
                            const lessonTitle = lesson.title || lesson.lesson_title || lesson.name || `Lesson ${lessonIndex + 1}`;

                            console.log('📄 Lesson data:', {
                                index: lessonIndex,
                                lesson: lesson,
                                id: lessonId,
                                title: lessonTitle
                            });

                            const isCompleted = completedLessons.includes(lessonId);
                            const isFirstLesson = moduleIndex === 0 && lessonIndex === 0;
                            const isPreviousCompleted = isFirstLesson || completedLessons.includes(parseInt(module.lessons[lessonIndex - 1]?.id_lesson || module.lessons[lessonIndex - 1]?.id));
                            const isUnlocked = isFirstLesson || isPreviousCompleted;

                            const lessonType = lesson.type_lesson || lesson.lesson_type || lesson.type || 'video';
                            const typeIcon = lessonType === 'video' ? 'play-circle' :
                                           lessonType === 'quiz' ? 'question-circle' : 'file-text';

                            let statusIcon = '';
                            let statusClass = '';

                            if (isCompleted) {
                                statusIcon = 'check-circle-fill';
                                statusClass = 'completed';
                            } else if (isUnlocked) {
                                statusIcon = 'play-circle';
                                statusClass = 'available';
                            } else {
                                statusIcon = 'lock';
                                statusClass = 'locked';
                            }

                            return `
                                <div class="lesson-item" onclick="navigateToLessonFromModal(${lessonId})" data-lesson-id="${lessonId}">
                                    <div class="lesson-icon">
                                        <i class="bi bi-${typeIcon}"></i>
                                    </div>
                                    <div class="lesson-info">
                                        <div class="lesson-name">${lessonTitle}</div>
                                        <div class="lesson-type">${lessonType} lesson</div>
                                    </div>
                                    <div class="lesson-status ${statusClass}">
                                        <i class="bi bi-${statusIcon}"></i>
                                    </div>
                                </div>
                            `;
                        }).join('');
                    }

                    const moduleTitle = module.title || module.module_title || module.name || `Module ${moduleIndex + 1}`;

                    contentHtml += `
                        <div class="course-module">
                            <div class="module-header" onclick="toggleModalModule('${moduleId}')">
                                <h4 class="module-title">${moduleTitle}</h4>
                                <i class="bi bi-chevron-right module-chevron" id="chevron-${moduleId}"></i>
                            </div>
                            <div class="module-lessons" id="${moduleId}">
                                ${lessonsHtml}
                            </div>
                        </div>
                    `;
                });

                container.innerHTML = contentHtml;

                // Expandir el primer módulo por defecto
                setTimeout(() => {
                    const firstModule = document.getElementById('modal-module-0');
                    const firstChevron = document.getElementById('chevron-modal-module-0');
                    if (firstModule && firstChevron) {
                        firstModule.classList.add('show');
                        firstChevron.classList.add('rotated');
                    }
                }, 100);
            }

            /**
             * Toggle módulo en el modal
             */
            function toggleModalModule(moduleId) {
                const moduleContent = document.getElementById(moduleId);
                const chevron = document.getElementById(`chevron-${moduleId}`);

                console.log('🔄 Toggling module:', {
                    moduleId: moduleId,
                    moduleContent: !!moduleContent,
                    chevron: !!chevron,
                    isCurrentlyShown: moduleContent?.classList.contains('show')
                });

                if (moduleContent && chevron) {
                    const isShown = moduleContent.classList.contains('show');

                    if (isShown) {
                        moduleContent.classList.remove('show');
                        chevron.classList.remove('rotated');
                    } else {
                        moduleContent.classList.add('show');
                        chevron.classList.add('rotated');
                    }

                    console.log('✅ Module toggled:', {
                        moduleId: moduleId,
                        newState: moduleContent.classList.contains('show') ? 'expanded' : 'collapsed'
                    });
                } else {
                    console.error('❌ Module elements not found:', {
                        moduleId: moduleId,
                        moduleContent: !!moduleContent,
                        chevron: !!chevron
                    });
                }
            }

            // Hacer la función global para que funcione con onclick
            window.toggleModalModule = toggleModalModule;

            /**
             * Navegar a lección desde el modal
             */
            function navigateToLessonFromModal(lessonId) {
                closeCourseContentModal();
                navigateToLesson(lessonId);
            }

            // DEBUG FUNCTION - Remove after testing
            // window.debugLessonState = debugLessonState; // Commented out - function not defined

            })(); // Cerrar función anónima - Sistema de Verificación Robusta ASG

        
        </script>
    <?php endif; ?>
</body>
</html>
<?php
}
/**
 * Shortcode para renderizar la página de lecciones
 * Uso: [asg_lessons]
 */
function asg_lessons_shortcode($atts) {
    // Capturar la salida en buffer
    ob_start();

    // Renderizar la página
    asg_render_lessons_page();

    // Retornar el contenido capturado
    return ob_get_clean();
}

// Registrar el shortcode
add_shortcode('asg_lessons', 'asg_lessons_shortcode');

function asg_display_lessons() {
    echo do_shortcode('[asg_lessons]');
}

