/**
 * Página de Detalle de Curso Profesional - ASG Learning Platform
 * Versión mejorada con paleta de colores profesional
 */
function asg_enhanced_course_detail_page() {
    $site_url = get_site_url();
    $course_code = isset($_GET['course']) ? sanitize_text_field($_GET['course']) : '';

 

    // Obtener precio real del curso desde la base de datos
    global $wpdb;
    $course_data = $wpdb->get_row($wpdb->prepare(
        "SELECT price_course FROM {$wpdb->prefix}courses WHERE code_course = %s",
        $course_code
    ));

    if ($course_data && !empty($course_data->price_course)) {
        $course_price = floatval($course_data->price_course);
    }
?>
    <!DOCTYPE html>
    <html lang="es">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Course Details - ASG Learning Platform</title>
        
        <!-- Bootstrap CSS -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
        <!-- Bootstrap Icons -->
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.2/font/bootstrap-icons.css">
        <!-- Google Fonts -->
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
        <!-- Animate.css -->
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
        
        <style>
            :root {
                /* Ability Seminars Group Color Palette */
                --primary-blue: #0C1B40;
                --primary-dark: #0C1B40;
                --primary-gradient: linear-gradient(135deg, #0C1B40 0%, #0C1B40 50%, #183681ff 100%);
                --secondary-gradient: linear-gradient(135deg, #0C1B40 0%, #183681ff 100%);
                --accent-yellow: #ffc107;
                --accent-gold: #ffb300;
                --light-blue: #e3f2fd;
                --success-color: #4caf50;
                --warning-color: #ff9800;
                --text-dark: #212121;
                --text-secondary: #757575;
                --background-light: #f5f5f5;
                --white: #ffffff;
                --card-shadow: 0 8px 32px #0f3f692d;
                --card-shadow-hover: 0 16px 48px #0f3f692d;
                --border-radius: 16px;
                --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }

            * {
                box-sizing: border-box;
            }

            body {
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
                background-color: var(--background-light);
                color: var(--text-dark);
                line-height: 1.6;
                margin: 0;
                padding: 0;
            }

            /* Navigation */
            .navbar {
                background: var(--white) !important;
                box-shadow: 0 2px 20px rgba(30, 136, 229, 0.08);
                border-bottom: 1px solid rgba(30, 136, 229, 0.06);
                transition: var(--transition);
                padding: 1rem 0;
            }

            .navbar-brand {
                font-weight: 800;
                font-size: 1.75rem;
                color: var(--primary-blue) !important;
                text-decoration: none;
            }

            .navbar-brand:hover {
                color: var(--primary-dark) !important;
            }

            .nav-link {
                color: var(--text-dark) !important;
                font-weight: 500;
                padding: 0.75rem 1rem !important;
                border-radius: 8px;
                transition: var(--transition);
            }

            .nav-link:hover {
                color: var(--accent-blue) !important;
                background-color: var(--light-blue);
            }

            .btn-outline-primary {
                border-color: var(--primary-blue);
                color: var(--primary-blue);
                font-weight: 600;
                padding: 0.75rem 1.5rem;
                border-radius: 12px;
                transition: var(--transition);
            }

            .btn-outline-primary:hover {
                background-color: var(--primary-blue);
                border-color: var(--primary-blue);
                color: var(--white);
                transform: translateY(-2px);
            }

            .btn-primary {
                background: var(--primary-gradient);
                border: none;
                color: var(--white);
                font-weight: 600;
                padding: 0.75rem 1.5rem;
                border-radius: 12px;
                transition: var(--transition);
            }

            .btn-primary:hover {
                background: var(--secondary-gradient);
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(30, 136, 229, 0.25);
            }

            /* Hero Section */
            .course-hero {
                background: var(--primary-gradient);
                position: relative;
                overflow: hidden;
                min-height: 90vh;
                display: flex;
                align-items: center;
                color: var(--white);
                padding: 4rem 0;
				
            }

            .course-hero::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="150" cy="150" r="80" fill="url(%23a)"/><circle cx="850" cy="250" r="120" fill="url(%23a)"/><circle cx="300" cy="800" r="100" fill="url(%23a)"/><circle cx="700" cy="600" r="90" fill="url(%23a)"/></svg>');
                opacity: 0.4;
            }

            .course-hero .container {
                position: relative;
                z-index: 2;
            }

            .course-image {
                border-radius: var(--border-radius);
                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
                transition: var(--transition);
                width: 100%;
                height: 400px;
                object-fit: cover;
            }

            .course-image:hover {
                transform: scale(1.02);
                box-shadow: 0 25px 80px rgba(0, 0, 0, 0.4);
            }

            .course-image-container {
                position: relative;
                overflow: hidden;
                border-radius: var(--border-radius);
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                padding: 1rem;
            }

            .course-image-overlay {
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                background: linear-gradient(transparent, rgba(0,0,0,0.7));
                padding: 2rem;
                color: white;
            }

            /* Stats Cards */
            .stats-card {
                background: rgba(255, 255, 255, 0.15);
                backdrop-filter: blur(20px);
                border-radius: 16px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                transition: var(--transition);
                padding: 1.5rem;
                text-align: center;
                color: var(--white);
            }

            .stats-card:hover {
                transform: translateY(-8px);
                background: rgba(255, 255, 255, 0.2);
                box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
            }

            .stats-card i {
                font-size: 2.5rem;
                margin-bottom: 0.75rem;
                opacity: 0.9;
            }

            .stats-card .fw-bold {
                font-size: 2rem;
                font-weight: 700;
                margin-bottom: 0.25rem;
            }

            .stats-card small {
                opacity: 0.8;
                font-weight: 500;
            }

            /* Enhanced Cards */
            .enhanced-card {
                border: none;
                border-radius: var(--border-radius);
                box-shadow: var(--card-shadow);
                transition: var(--transition);
                overflow: hidden;
                background: var(--white);
                margin-bottom: 2rem;
            }

            .enhanced-card:hover {
                transform: translateY(-4px);
                box-shadow: var(--card-shadow-hover);
            }

            .enhanced-card .card-header {
                background: linear-gradient(135deg, var(--light-blue) 0%, rgba(63, 81, 181, 0.08) 100%);
                border-bottom: 1px solid rgba(63, 81, 181, 0.12);
                padding: 2rem;
                border-radius: var(--border-radius) var(--border-radius) 0 0;
            }

            .enhanced-card .card-header h4 {
                color: var(--primary-dark);
                font-weight: 700;
                margin: 0;
            }

            /* Module Cards */
            .module-card {
                border: 1px solid rgba(63, 81, 181, 0.12);
                border-radius: var(--border-radius);
                transition: var(--transition);
                margin-bottom: 1.5rem;
                overflow: hidden;
                background: var(--white);
            }

            .module-card:hover {
                border-color: var(--accent-blue);
                box-shadow: 0 8px 32px rgba(63, 81, 181, 0.15);
                transform: translateY(-2px);
            }

            .module-header {
                background: linear-gradient(135deg, var(--light-blue) 0%, rgba(63, 81, 181, 0.06) 100%);
                padding: 1.75rem;
                border-bottom: 1px solid rgba(63, 81, 181, 0.08);
                cursor: pointer;
                transition: var(--transition);
            }

            .module-header:hover {
                background: linear-gradient(135deg, rgba(63, 81, 181, 0.08) 0%, rgba(63, 81, 181, 0.12) 100%);
            }

            .module-number {
                width: 48px;
                height: 48px;
                border-radius: 12px;
                background: var(--primary-gradient);
                color: var(--white);
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: 700;
                font-size: 1.25rem;
                box-shadow: 0 4px 12px rgba(26, 35, 126, 0.3);
            }

            /* Lesson Items */
            .lesson-item {
                padding: 1.25rem 1.75rem;
                border-bottom: 1px solid rgba(63, 81, 181, 0.06);
                transition: var(--transition);
                cursor: pointer;
                position: relative;
            }

            .lesson-item:last-child {
                border-bottom: none;
            }

            .lesson-item:hover {
                background: rgba(63, 81, 181, 0.04);
                padding-left: 2.25rem;
            }

            .lesson-item.preview {
                background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
                border-left: 4px solid var(--warning-color);
            }

            .lesson-item.preview:hover {
                background: linear-gradient(135deg, #ffecb3 0%, #ffe082 100%);
            }

            /* Estilos para lecciones completadas */
            .lesson-item.completed {
                background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
                border-left: 4px solid #28a745;
            }

            .lesson-item.completed:hover {
                background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            }

            .lesson-item.completed .lesson-icon {
                background: rgba(40, 167, 69, 0.1);
                color: #28a745;
            }

            /* Estilos para lecciones bloqueadas */
            .lesson-item.locked {
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                border-left: 4px solid #6c757d;
                cursor: not-allowed;
            }

            .lesson-item.locked:hover {
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                padding-left: 2rem; /* No hover effect */
            }

            .lesson-item.locked .lesson-icon {
                background: rgba(108, 117, 125, 0.1);
                color: #6c757d;
            }

            .lesson-item.locked .lesson-title {
                color: #6c757d;
            }

            /* Indicador de progreso en lecciones */
            .lesson-item::after {
                content: '';
                position: absolute;
                right: 15px;
                top: 50%;
                transform: translateY(-50%);
                width: 20px;
                height: 20px;
                border-radius: 50%;
                display: none;
            }

            .lesson-item.completed::after {
                content: '✓';
                display: flex;
                align-items: center;
                justify-content: center;
                background: #28a745;
                color: white;
                font-size: 12px;
                font-weight: bold;
            }

            .lesson-item.locked::after {
                content: '🔒';
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
            }

            .lesson-icon {
                width: 40px;
                height: 40px;
                border-radius: 10px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 1.2rem;
                margin-right: 1rem;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }

            .lesson-icon.video { 
                background: linear-gradient(135deg, #e53935 0%, #c62828 100%); 
                color: var(--white); 
            }
            .lesson-icon.text { 
                background: linear-gradient(135deg, #0C1B40 0%, #183681ff 100%); 
                color: var(--white); 
            }
            .lesson-icon.quiz { 
                background: linear-gradient(135deg, var(--accent-blue) 0%, var(--primary-blue) 100%); 
                color: var(--primary-blue); 
            }
            .lesson-icon.assignment { 
                background: linear-gradient(135deg, #8e24aa 0%, #7b1fa2 100%); 
                color: var(--white); 
            }

            /* Price Card */
            .price-card {
                background: var(--primary-gradient);
                border-radius: var(--border-radius);
                color: var(--white);
                position: relative;
                overflow: hidden;
                box-shadow: 0 12px 40px rgba(26, 35, 126, 0.3);
            }

            .price-card::before {
                content: '';
                position: absolute;
                top: -50%;
                left: -50%;
                width: 200%;
                height: 200%;
                background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
                animation: shimmer 4s infinite;
            }

            @keyframes shimmer {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            .enroll-btn {
                background: var(--accent-yellow);
                border: 2px solid var(--accent-gold);
                color: var(--text-dark);
                font-weight: 700;
                padding: 1.25rem 2rem;
                border-radius: 12px;
                transition: var(--transition);
                position: relative;
                overflow: hidden;
                font-size: 1.1rem;
                text-transform: uppercase;
                letter-spacing: 1px;
            }

            .enroll-btn:hover {
                background: var(--accent-gold);
                color: var(--text-dark);
                transform: translateY(-3px);
                box-shadow: 0 12px 35px rgba(255, 193, 7, 0.4);
            }

            .enroll-btn::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
                transition: left 0.6s;
            }

            .enroll-btn:hover::before {
                left: 100%;
            }

            /* Loading Animation */
            .loading-container {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                min-height: 60vh;
            }

            .loading-spinner {
                width: 60px;
                height: 60px;
                border: 4px solid rgba(255, 255, 255, 0.2);
                border-left: 4px solid var(--white);
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin-bottom: 1.5rem;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            /* Breadcrumb */
            .breadcrumb {
                background: rgba(255, 255, 255, 0.15);
                border-radius: 12px;
                padding: 1rem 1.5rem;
                backdrop-filter: blur(15px);
                border: 1px solid rgba(255, 255, 255, 0.2);
            }

            .breadcrumb-item a {
                color: rgba(255, 255, 255, 0.85);
                text-decoration: none;
                transition: var(--transition);
                font-weight: 500;
            }

            .breadcrumb-item a:hover {
                color: var(--white);
            }

            .breadcrumb-item.active {
                color: var(--white);
                font-weight: 600;
            }

            /* Badges */
            .category-badge {
                background: rgba(255, 255, 255, 0.15);
                border: 1px solid rgba(255, 255, 255, 0.25);
                color: var(--white);
                padding: 0.75rem 1.25rem;
                border-radius: 25px;
                font-weight: 600;
                backdrop-filter: blur(10px);
                font-size: 0.9rem;
				text-transform: capitalize;
            }

            .preview-badge {
                background: linear-gradient(135deg, var(--accent-yellow) 0%, var(--accent-gold) 100%);
                color: var(--text-dark);
                font-size: 0.75rem;
                padding: 0.4rem 0.8rem;
                border-radius: 15px;
                font-weight: 700;
                box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3);
				display: none;
            }

            /* Objectives and Benefits */
            .objective-item, .benefit-item {
                display: flex;
                align-items: flex-start;
                padding: 1rem;
                margin-bottom: 0.75rem;
                border-radius: 12px;
                transition: var(--transition);
                background: rgba(63, 81, 181, 0.03);
                border: 1px solid rgba(63, 81, 181, 0.08);
            }

            .objective-item:hover, .benefit-item:hover {
                background: rgba(63, 81, 181, 0.06);
                transform: translateX(8px);
                border-color: rgba(63, 81, 181, 0.15);
            }

            .objective-item i, .benefit-item i {
                margin-right: 1rem;
                margin-top: 0.25rem;
                font-size: 1.2rem;
                color: var(--success-color);
            }

            /* Course Features */
            .course-features {
                background: var(--white);
                border-radius: var(--border-radius);
                box-shadow: var(--card-shadow);
                padding: 2rem;
            }

            .feature-item {
                display: flex;
                align-items: center;
                padding: 1rem 0;
                border-bottom: 1px solid rgba(63, 81, 181, 0.06);
                transition: var(--transition);
            }

            .feature-item:last-child {
                border-bottom: none;
            }

            .feature-item:hover {
                color: var(--accent-blue);
                transform: translateX(5px);
            }

            .feature-item i {
                color: var(--success-color);
                margin-right: 1rem;
                font-size: 1.2rem;
            }

            /* ASG Brand Enhancements */
            .text-primary {
                color: var(--primary-blue) !important;
            }

            .bg-primary {
                background-color: var(--primary-blue) !important;
            }

            .border-primary {
                border-color: var(--primary-blue) !important;
            }

            .btn-warning {
                background-color: var(--accent-yellow);
                border-color: var(--accent-gold);
                color: var(--text-dark);
            }

            .btn-warning:hover {
                background-color: var(--accent-gold);
                border-color: var(--accent-gold);
                color: var(--text-dark);
            }

            /* Responsive Design */
            @media (max-width: 768px) {
                .course-hero {
                    min-height: 60vh;
                    text-align: center;
                    padding: 2rem 0;
                }
                
                .course-image {
                    height: 300px;
                    margin-top: 2rem;
                }
                
                .stats-card {
                    margin-bottom: 1rem;
                }
                
                .module-card {
                    margin-bottom: 1rem;
                }

                .enhanced-card .card-header {
                    padding: 1.5rem;
                }

                .lesson-item {
                    padding: 1rem 1.25rem;
                }

                .enroll-btn {
                    padding: 1rem 1.5rem;
                    font-size: 1rem;
                }
            }

            /* Animations */
            .fade-in {
                animation: fadeIn 0.8s ease-out;
            }

            .slide-up {
                animation: slideUp 0.8s ease-out;
            }

            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }

            @keyframes slideUp {
                from {
                    opacity: 0;
                    transform: translateY(40px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            /* Sticky Sidebar */
            .sticky-sidebar {
                position: sticky;
                top: 100px;
                z-index: 10;
            }

            /* Additional Professional Touches */
            .display-4 {
                font-weight: 800;
                line-height: 1.2;
            }

            .lead {
                font-weight: 400;
                opacity: 0.9;
                font-size: 1.2rem;
            }

            .text-muted {
                color: var(--text-secondary) !important;
            }

            .fw-bold {
                font-weight: 700 !important;
            }

            .fw-medium {
                font-weight: 600 !important;
            }

            /* Toast Notifications */
            .toast {
                border-radius: 12px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            }

            .bg-success {
                background: linear-gradient(135deg, var(--success-color) 0%, #388e3c 100%) !important;
            }

            .bg-primary {
                background: var(--primary-gradient) !important;
            }
        </style>
    </head>
    <body>
        <!-- Navigation -->
        

        <!-- Loading State -->
        <div id="loadingState" class="course-hero">
            <div class="container">
                <div class="loading-container text-white">
                    <div class="loading-spinner"></div>
                    <h4 class="animate__animated animate__pulse animate__infinite">Loading course details...</h4>
                    <p class="opacity-75">Please wait a moment</p>
                </div>
            </div>
        </div>

        <!-- Course Hero Section -->
        <section id="courseHero" class="course-hero text-white" style="display: none;">
            <div class="container py-5">
                <div class="row align-items-center">
                    <div class="col-lg-7">
                        <nav aria-label="breadcrumb" class="mb-4">
                            <ol class="breadcrumb">
                                <li class="breadcrumb-item">
                                    <a href="<?php echo $site_url; ?>/my-programs/">
                                        <i class="bi bi-house me-1"></i>Courses
                                    </a>
                                </li>
                                <li class="breadcrumb-item active" id="courseBreadcrumb">Course Details</li>
                            </ol>
                        </nav>
                        
                        <div class="mb-4">
                            <span id="categoryBadge" class="category-badge me-3">
                                <i class="bi bi-laptop me-1"></i>Categoría
                            </span>
                       
                        </div>
                        
                        <h1 id="courseTitle" class="display-4 fw-bold mb-4 animate__animated animate__fadeInUp">
                            Título del Curso
                        </h1>
                        
                        <p id="courseDescription" class="lead mb-4 animate__animated animate__fadeInUp animate__delay-1s">
                            La descripción del curso aparecerá aquí...
                        </p>
                        
                        <div class="row g-3 mb-4 animate__animated animate__fadeInUp animate__delay-2s">
                            <div class="col-6 col-md-3">
                                <div class="stats-card">
                                    <i class="bi bi-collection"></i>
                                    <div class="fw-bold fs-4" id="modulesCount">0</div>
                                    <small>Modules</small>
                                </div>
                            </div>
                            <div class="col-6 col-md-3">
                                <div class="stats-card">
                                    <i class="bi bi-play-circle"></i>
                                    <div class="fw-bold fs-4" id="lessonsCount">0</div>
                                    <small>Lessons</small>
                                </div>
                            </div>
                            <div class="col-6 col-md-3">
                                <div class="stats-card">
                                    <i class="bi bi-clock"></i>
                                    <div class="fw-bold fs-4" id="courseDuration">0</div>
                                    <small>Minutes</small>
                                </div>
                            </div>
                            <div class="col-6 col-md-3">
                                <div class="stats-card">
                                    <i class="bi bi-globe"></i>
                                    <div class="fw-bold fs-4" id="courseLanguage">EN</div>
                                    <small>Language</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-lg-5 text-center">
                        <div class="animate__animated animate__fadeInRight animate__delay-1s">
                            <div class="course-image-container">
                                <img id="courseImage" src="https://via.placeholder.com/600x400/1a237e/ffffff?text=Curso" alt="Imagen del Curso" class="course-image">
                                <div class="course-image-overlay">
<!--                                     <button class="btn btn-light btn-lg" onclick="previewCourse()">
                                        <i class="bi bi-play-circle me-2"></i>
                                        Course Preview
                                    </button> -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Course Content -->
        <section class="py-5" style="margin-top: 0;">
            <div class="container">
                <div class="row">
                    <!-- Main Content -->
                    <div class="col-lg-8">
                        <!-- Learning Objectives -->
                        <div class="enhanced-card mb-4 fade-in">
                            <div class="card-header">
                                <h4 class="fw-bold mb-0">
                                    <i class="bi bi-target text-primary me-2"></i>
                                    What You'll Learn
                                </h4>
                            </div>
                            <div class="card-body p-4">
                                <div id="objectivesList" class="row g-2">
                                    <!-- Learning objectives will be loaded here -->
                                </div>
                            </div>
                        </div>

                        <!-- Course Benefits -->
                        <div class="enhanced-card mb-4 slide-up">
                            <div class="card-header">
                                <h4 class="fw-bold mb-0">
                                    <i class="bi bi-star text-warning me-2"></i>
                                    Course Benefits
                                </h4>
                            </div>
                            <div class="card-body p-4">
                                <div id="benefitsList" class="row g-2">
                                    <!-- Course benefits will be loaded here -->
                                </div>
                            </div>
                        </div>

                        <!-- Course Curriculum -->
                        <div class="enhanced-card fade-in">
                            <div class="card-header">
                                <h4 class="fw-bold mb-0">
                                    <i class="bi bi-list-ul text-primary me-2"></i>
                                    Course Content
                                </h4>
                                <p class="text-muted mb-0 mt-2">
                                    <span id="totalLessons">0</span> lessons •
                                    <span id="totalDuration">0</span> hours of content
                                </p>
                            </div>
                            <div class="card-body p-0">
                                <div id="modulesList">
                                    <!-- Course modules will be loaded here -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sidebar -->
                    <div class="col-lg-4">
                        <div class="sticky-sidebar">
                            <!-- Price Card -->
                            <div class="price-card p-4 mb-4 text-center animate__animated animate__fadeInUp">
                                <div class="position-relative">
                                    <div class="display-3 fw-bold mb-2" id="coursePrice">Gratis</div>
                                    <div id="originalPrice" class="text-white-50 mb-3" style="display: none;">
                                        <span class="text-decoration-line-through fs-5" id="originalPriceValue"></span>
                                        <span class="badge bg-warning text-dark ms-2">¡Discount!</span>
                                    </div>
                                    <p class="mb-4 opacity-75">Full lifetime access</p>
                                    <button class="enroll-btn btn btn-lg w-100" onclick="enrollInCourse()">
                                        <i class="bi bi-plus-circle me-2"></i>
                                        <span>Enroll Now</span>
                                    </button>
                                    <p class="mt-3 mb-0 small opacity-75">
                                        <i class="bi bi-shield-check me-1"></i>
                                        30-day money-back guarantee
                                    </p>
                                </div>
                            </div>

                            <!-- Course Features -->
                            <div class="course-features animate__animated animate__fadeInUp animate__delay-1s">
                                <h5 class="fw-bold mb-3">
                                    <i class="bi bi-check-circle text-success me-2"></i>
                                    This course includes:
                                </h5>
                                <div class="feature-item">
                                    <i class="bi bi-infinity"></i>
                                    <span>Full lifetime access</span>
                                </div>
                                <div class="feature-item">
                                    <i class="bi bi-phone"></i>
                                    <span>Access on mobile and desktop</span>
                                </div>
                                <div class="feature-item">
                                    <i class="bi bi-award"></i>
                                    <span>Certificate of Completion</span>
                                </div>
                                <div class="feature-item">
                                    <i class="bi bi-download"></i>
                                    <span>Downloadable Resources</span>
                                </div>
                                <div class="feature-item">
                                    <i class="bi bi-people"></i>
                                    <span>Community Access</span>
                                </div>
                                <div class="feature-item">
                                    <i class="bi bi-arrow-clockwise"></i>
                                    <span>Free updates</span>
                                </div>
                            </div>


                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Bootstrap JS -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

        <script>
            function showModal(title, html) {
                const modalEl = document.getElementById('genericModal');
                document.getElementById('genericModalLabel').textContent = title;
                document.getElementById('genericModalBody').innerHTML = html;
                new bootstrap.Modal(modalEl).show();
            }
        </script>

        
        <script>
            // Configuración de la API
            const API_BASE = '<?php echo $site_url; ?>/wp-json/asg/v1';
            const COURSE_CODE = '<?php echo $course_code; ?>';
            
            // Variables globales
            let currentCourse = null;
            let isEnrolled = false;

            // Inicializar página
            document.addEventListener('DOMContentLoaded', function() {
                if (COURSE_CODE) {
                    loadCourseDetail();

                    // Verificar estado de inscripción y cargar progreso
                    setTimeout(async () => {
                        await checkEnrollmentStatus();
                        if (isEnrolled) {
                            loadUserProgressForPreview();
                        }
                    }, 2000);
                } else {
                    showError('No se especificó ningún curso');
                }

                // Agregar efectos de scroll
                addScrollEffects();
            });

            // Cargar detalles del curso
            async function loadCourseDetail() {
                try {
                    showLoadingState();
                    
                    const courseResponse = await fetch(`${API_BASE}/courses/api/${COURSE_CODE}?include=modules,lessons,objectives`);
                    const courseData = await courseResponse.json();
                    
                    console.log('📚 Course data received:', courseData);
                    console.log('🔍 API URL called:', `${API_BASE}/courses/api/${COURSE_CODE}?include=modules,lessons,objectives`);
                    
                    if (courseData.success && courseData.data) {
                        // El API devuelve data como objeto directo, no array
                        if (courseData.data.code_course === COURSE_CODE) {
                            currentCourse = courseData.data;
                        } else {
                            showError('Course not found');
                            return;
                        }
                        
                        console.log('📦 Course modules:', currentCourse.modules);

                        // El endpoint optimizado ya incluye módulos y lecciones
                        console.log('📦 Modules from optimized API:', currentCourse.modules?.length || 0);
                        
                        // Verificar que los módulos tengan lecciones incluidas
                        if (currentCourse.modules) {
                            currentCourse.modules.forEach((module, index) => {
                                console.log(`📖 Module ${index + 1} (${module.title_module}):`, module.lessons?.length || 0, 'lessons');
                            });
                        }
                        
                        await renderCourseDetail();
                        hideLoadingState();
                    } else {
                        showError('Course not found');
                    }
                } catch (error) {
                    console.error('Error cargando curso:', error);
                    console.error('API Response:', error.response);
                    showError('Error loading course details. Please try again.');
                }
            }

            // Renderizar detalles del curso
            async function renderCourseDetail() {
                const course = currentCourse;
                
                // Actualizar título de la página
                document.title = `${course.name_course} - ASG Learning Platform`;

                // Actualizar breadcrumb - verificar existencia
                const courseBreadcrumb = document.getElementById('courseBreadcrumb');
                if (courseBreadcrumb) {
                    courseBreadcrumb.textContent = course.name_course;
                }
                
                // Actualizar información de categoría - verificar existencia
                const categoryInfo = getCategoryInfo(course.category_course);
                const categoryBadge = document.getElementById('categoryBadge');
                if (categoryBadge) {
                    categoryBadge.innerHTML = `<i class="${categoryInfo.icon} me-1"></i>${categoryInfo.label}`;
                }
                
                // Actualizar información del curso - verificar existencia
                const courseTitle = document.getElementById('courseTitle');
                if (courseTitle) {
                    courseTitle.textContent = course.name_course;
                }

                const courseDescription = document.getElementById('courseDescription');
                if (courseDescription) {
                    courseDescription.textContent = course.description_course;
                }
                
              
                
                // Calcular estadísticas reales
                let totalModules = 0;
                let totalLessons = 0;
                let totalDurationMinutes = 0;

                if (course.modules && Array.isArray(course.modules)) {
                    totalModules = course.modules.length;
                    
                    course.modules.forEach(module => {
                        if (module.lessons && Array.isArray(module.lessons)) {
                            totalLessons += module.lessons.length;
                            
                            module.lessons.forEach(lesson => {
                                totalDurationMinutes += parseInt(lesson.duration_lesson) || 0;
                            });
                        }
                    });
                }

                // Actualizar estadísticas - verificar existencia
                const modulesCount = document.getElementById('modulesCount');
                if (modulesCount) {
                    modulesCount.textContent = totalModules;
                }

                const lessonsCount = document.getElementById('lessonsCount');
                if (lessonsCount) {
                    lessonsCount.textContent = totalLessons;
                }

                const courseDuration = document.getElementById('courseDuration');
                if (courseDuration) {
                    courseDuration.textContent = totalDurationMinutes;
                }

                const courseLanguage = document.getElementById('courseLanguage');
                if (courseLanguage) {
                    courseLanguage.textContent = (course.language_course || 'en').toUpperCase();
                }

                // Actualizar totales en el curriculum - verificar existencia
                const totalLessonsElement = document.getElementById('totalLessons');
                if (totalLessonsElement) {
                    totalLessonsElement.textContent = totalLessons;
                }

                const totalDurationElement = document.getElementById('totalDuration');
                if (totalDurationElement) {
                    totalDurationElement.textContent = Math.floor(totalDurationMinutes / 60);
                }
                
                // Actualizar imagen del curso - verificar existencia
                const imageUrl = course.cover_img || course.large_url || 'https://via.placeholder.com/600x400/1a237e/ffffff?text=Curso';
                const courseImage = document.getElementById('courseImage');
                if (courseImage) {
                    courseImage.src = imageUrl;
                    courseImage.alt = course.name_course;
                }
                
                // Actualizar precio
                updatePriceDisplay(course.price_course);
                
                // Renderizar objetivos y beneficios
                renderObjectives(course.objectives);
                renderBenefits(course.benefits);
                
                // Renderizar módulos
                console.log('🎯 Rendering modules:', course.modules);
                renderModules(course.modules || []);
                
                // Mostrar sección hero
                document.getElementById('courseHero').style.display = 'block';
                
                // Agregar animaciones
                setTimeout(() => {
                    addEntranceAnimations();
                }, 100);
            }

            // Actualizar visualización del precio
            function updatePriceDisplay(price) {
                const priceElement = document.getElementById('coursePrice');
                const originalPriceElement = document.getElementById('originalPrice');
                const originalPriceValue = document.getElementById('originalPriceValue');
                
                const numericPrice = parseFloat(price) || 0;
                
                if (numericPrice > 0) {
                    priceElement.textContent = `$${numericPrice}`;
                    const originalPrice = Math.round(numericPrice * 1.5);
                    originalPriceValue.textContent = `$${originalPrice}`;
                    originalPriceElement.style.display = 'block';
                } else {
                    priceElement.textContent = 'Gratis';
                    originalPriceElement.style.display = 'none';
                }
            }

            // Renderizar objetivos de aprendizaje
            function renderObjectives(objectives) {
                const container = document.getElementById('objectivesList');
                let objectivesList = [];
                
                if (typeof objectives === 'string' && objectives.trim()) {
                    // Si contiene separadores, dividir; si no, usar como un solo objetivo
                    objectivesList = objectives.includes('|') 
                        ? objectives.split('|').filter(obj => obj.trim())
                        : [objectives.trim()];
                }
                
                if (objectivesList.length === 0) {
                    container.innerHTML = '<div class="col-12"><p class="text-muted">No learning objectives specified.</p></div>';
                    return;
                }
                
                container.innerHTML = objectivesList.map(objective => `
                    <div class="col-md-10 mb-3">
                        <div class="objective-item">
                            <i class="bi bi-check-circle text-success"></i>
                            <span>${objective.trim()}</span>
                        </div>
                    </div>
                `).join('');
            }

            // Renderizar beneficios del curso
            function renderBenefits(benefits) {
                const container = document.getElementById('benefitsList');
                let benefitsList = [];
                
                if (typeof benefits === 'string' && benefits.trim()) {
                    benefitsList = benefits.includes('|') 
                        ? benefits.split('|').filter(ben => ben.trim())
                        : [benefits.trim()];
                }
                
                if (benefitsList.length === 0) {
                    container.innerHTML = '<div class="col-12"><p class="text-muted">No course benefits specified.</p></div>';
                    return;
                }
                
                container.innerHTML = benefitsList.map(benefit => `
                    <div class="col-md-10 mb-3">
                        <div class="benefit-item">
                            <i class="bi bi-star-fill text-warning"></i>
                            <span>${benefit.trim()}</span>
                        </div>
                    </div>
                `).join('');
            }

            // Renderizar módulos del curso
            function renderModules(modules) {
                const container = document.getElementById('modulesList');
                console.log('📋 renderModules called with:', modules);

                if (!modules || modules.length === 0) {
                    console.log('❌ No modules found');
                    container.innerHTML = '<div class="p-4"><p class="text-muted mb-0">No modules available for this course.</p></div>';
                    return;
                }

                console.log(`✅ Rendering ${modules.length} modules`);
                
                container.innerHTML = modules.map((module, index) => `
                    <div class="module-card">
                        <div class="module-header" onclick="toggleModule('module-${module.id_modules || index}')">
                            <div class="d-flex align-items-center justify-content-between">
                                <div class="d-flex align-items-center">
                                    <div class="module-number me-3 p-4">${index + 1}</div>
                                    <div>
                                        <h5 class="mb-1 fw-bold">${module.title_module}</h5>
                                        <p class="text-muted mb-0 small">
                                            ${module.lessons ? module.lessons.length : 0} lessons
                                            ${module.description_module ? ` • ${module.description_module}` : ''}
                                        </p>
                                    </div>
                                </div>
                                <i class="bi bi-chevron-down fs-5 text-primary" id="chevron-module-${module.id_modules || index}"></i>
                            </div>
                        </div>
                        <div class="collapse" id="module-${module.id_modules || index}">
                            <div class="card-body p-0">
                                ${renderLessons(module.lessons || [])}
                            </div>
                        </div>
                    </div>
                `).join('');
            }

            // Renderizar lecciones del módulo
            function renderLessons(lessons) {
                console.log('📖 renderLessons called with:', lessons);
                if (!lessons || lessons.length === 0) {
                    return '<div class="p-4"><p class="text-muted mb-0">No hay lecciones en este módulo.</p></div>';
                }
                
                return lessons.map(lesson => {
                    const isPreview = parseInt(lesson.is_preview) === 1;
                    const lessonType = lesson.lesson_type || 'text';
                    const icon = getLessonIcon(lessonType);
                    return `
                        <div class="lesson-item ${isPreview ? 'preview' : ''}" onclick="viewLesson('${lesson.id_lesson}', ${isPreview})">
                            <div class="d-flex align-items-center justify-content-between">
                                <div class="d-flex align-items-center">
                                    <div class="lesson-icon p-4 ${lessonType}">
                                        <i class="${icon}"></i>
                                    </div>
                                    <div>
                                        <span class="fw-medium">${lesson.title_lesson}</span>
                                        ${isPreview ? '<span class="preview-badge ms-2">Preview</span>' : ''}
                                    </div>
                                </div>
                                <div class="d-flex align-items-center text-muted small">
                                    <i class="bi bi-clock me-1"></i>
                                    <span>${lesson.duration_lesson || 5} min</span>
                                    ${isPreview
                                        ? '<i class="bi bi-unlock ms-2 text-success"></i>'
                                        : '<i class="bi bi-lock ms-2"></i>'
                                    }
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');
            }

            // Obtener información de categoría
            function getCategoryInfo(category) {
                const categories = {
                    'Technology': { label: 'Technology', icon: 'bi-laptop' },
                    'Business': { label: 'Business', icon: 'bi-briefcase' },
                    'Design': { label: 'Design', icon: 'bi-palette' },
                    'Marketing': { label: 'Marketing', icon: 'bi-megaphone' },
                    'Finance': { label: 'Finance', icon: 'bi-graph-up' },
                    'Personal Development': { label: 'Personal Development', icon: 'bi-code-slash' },
                };
                return categories[category] || { label: category || 'General', icon: 'bi-book' };
            }

            // Obtener icono de tipo de lección
            function getLessonIcon(lessonType) {
                const icons = {
                    'Text': 'bi-file-text',
                    'Video': 'bi-play-circle-fill',
                    'Quiz': 'bi-question-circle-fill',
                    'Assignment': 'bi-clipboard-check-fill'
                };
                return icons[lessonType] || 'bi-file-text';
            }

            // Alternar módulo
            function toggleModule(moduleId) {
                const module = document.getElementById(moduleId);
                const chevron = document.getElementById(`chevron-${moduleId}`);
                
                if (module.classList.contains('show')) {
                    module.classList.remove('show');
                    chevron.style.transform = 'rotate(0deg)';
                } else {
                    // Cerrar otros módulos
                    document.querySelectorAll('.collapse.show').forEach(openModule => {
                        openModule.classList.remove('show');
                    });
                    document.querySelectorAll('[id^="chevron-module-"]').forEach(chev => {
                        chev.style.transform = 'rotate(0deg)';
                    });
                    
                    // Abrir módulo actual
                    module.classList.add('show');
                    chevron.style.transform = 'rotate(180deg)';
                }
            }

            // ASG STUDENT FLOW - ENROLLMENT SYSTEM (DEFINIR PRIMERO)

            // Configuración del curso para ASG Student Flow
            const ASG_COURSE_CODE = '<?php echo $course_code; ?>';
            // No definir ASG_COURSE_PRICE aquí porque currentCourse puede no estar listo

            // Función principal de inscripción
            function startEnrollment(courseCode = ASG_COURSE_CODE, price = 10) {
                console.log('ASG: Iniciando proceso de inscripción', { courseCode, price });

                // Verificar si el usuario está logueado
                if (!isUserLoggedIn()) {
                    console.log('ASG: Usuario no logueado, mostrando modal');
                    showLoginModal(courseCode, price);
                    return;
                }

                // Usuario logueado - verificar si ya está inscrito
                checkExistingEnrollment(courseCode, price);
            }

            // Verificar si el usuario está logueado
            function isUserLoggedIn() {
                // Múltiples métodos de verificación para máxima compatibilidad
                return document.body.classList.contains('logged-in') ||
                       document.querySelector('.admin-bar') !== null ||
                       window.wp_user_logged_in === true ||
                       document.querySelector('body.logged-in') !== null;
            }

            // Verificar enrollment existente
            async function checkExistingEnrollment(courseCode, price) {
                try {
                    const response = await fetch(`/wp-json/asg/v1/check-enrollment?course=${courseCode}`);
                    const result = await response.json();

                    if (result.success && result.enrolled) {
                        // Ya está inscrito
                        showAlreadyEnrolledMessage(courseCode);
                    } else {
                        // No está inscrito - proceder al pago
                        redirectToPayment(courseCode, price);
                    }
                } catch (error) {
                    console.error('ASG: Error verificando enrollment:', error);
                    // En caso de error, proceder al pago (fail-safe)
                    redirectToPayment(courseCode, price);
                }
            }

            // Mostrar modal de login/registro
            function showLoginModal(courseCode, price) {
                // Crear modal si no existe
                let modal = document.getElementById('asg-login-modal');
                if (!modal) {
                    modal = createLoginModal();
                    document.body.appendChild(modal);
                }

                // Actualizar contenido del modal
                updateModalContent(modal, courseCode, price);

                // Mostrar modal
                modal.style.display = 'flex';
                document.body.style.overflow = 'hidden';
            }

            // Crear estructura del modal
            function createLoginModal() {
                const modal = document.createElement('div');
                modal.id = 'asg-login-modal';
                modal.className = 'asg-modal-overlay';

                modal.innerHTML = `
                    <div class="asg-modal-content">
                        <button class="asg-modal-close" onclick="closeLoginModal()">&times;</button>
                        <div id="asg-modal-body">
                            <!-- Contenido dinámico -->
                        </div>
                    </div>
                `;

                // Event listener para cerrar al hacer clic fuera
                modal.addEventListener('click', function(e) {
                    if (e.target === modal) {
                        closeLoginModal();
                    }
                });

                return modal;
            }

            // Actualizar contenido del modal
            function updateModalContent(modal, courseCode, price) {
                const modalBody = modal.querySelector('#asg-modal-body');
                const currentUrl = encodeURIComponent(window.location.href);
                const registerUrl = `/register/?redirect_course=${courseCode}&price=${price}`;
                const loginUrl = `/wp-login.php?redirect_to=${currentUrl}`;

                modalBody.innerHTML = `
                    <div class="login-modal-header">
                        <h3>🎓 Para inscribirte necesitas una cuenta</h3>
                        <p>Únete a miles de estudiantes que ya están aprendiendo</p>
                    </div>

                    <div class="login-options">
                        <a href="${registerUrl}" class="btn btn-primary btn-large">
                            <span class="btn-icon">✨</span>
                            Crear Cuenta Nueva
                            <small>Rápido y gratuito</small>
                        </a>

                        <div class="divider">
                            <span>o</span>
                        </div>

                        <a href="${loginUrl}" class="btn btn-outline-primary btn-large">
                            <span class="btn-icon">🔐</span>
                            Iniciar Sesión
                            <small>Ya tengo cuenta</small>
                        </a>
                    </div>

                    <div class="benefits-section">
                        <h5>¿Por qué crear una cuenta?</h5>
                        <ul class="benefits-list">
                            <li><span class="benefit-icon">✅</span> Acceso a todos tus cursos</li>
                            <li><span class="benefit-icon">📊</span> Seguimiento de progreso</li>
                            <li><span class="benefit-icon">🏆</span> Certificados descargables</li>
                            <li><span class="benefit-icon">💬</span> Soporte técnico incluido</li>
                        </ul>
                    </div>

                    <div class="course-reminder">
                        <div class="reminder-content">
                            <h6>📚 Curso seleccionado:</h6>
                            <p><strong>${courseCode}</strong> - $${price} USD</p>
                        </div>
                    </div>
                `;
            }

            // Cerrar modal
            function closeLoginModal() {
                const modal = document.getElementById('asg-login-modal');
                if (modal) {
                    modal.style.display = 'none';
                    document.body.style.overflow = '';
                }
            }

            // Mostrar mensaje de ya inscrito
            function showAlreadyEnrolledMessage(courseCode) {
                // Crear notificación temporal
                const notification = document.createElement('div');
                notification.className = 'asg-notification asg-success';
                notification.innerHTML = `
                    <div class="notification-content">
                        <h4>✅ Ya estás inscrito</h4>
                        <p>Ya tienes acceso a este curso.</p>
                        <div class="notification-actions">
                            <a href="/lessons/?course=${courseCode}" class="btn btn-success">Ir a las Lecciones</a>
                            <button onclick="this.parentElement.parentElement.parentElement.remove()" class="btn btn-secondary">Cerrar</button>
                        </div>
                    </div>
                `;

                // Insertar al inicio del body
                document.body.insertBefore(notification, document.body.firstChild);

                // Auto-remover después de 10 segundos
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.remove();
                    }
                }, 10000);
            }

            // Redirect al pago
            function redirectToPayment(courseCode, price) {
                const paymentUrl = `/payment/?course=${courseCode}&price=${price}`;
                console.log('ASG: Redirigiendo a pago:', paymentUrl);
                window.location.href = paymentUrl;
            }

            // Función global para ser llamada desde HTML
            window.startEnrollment = startEnrollment;

            // Acciones del curso - INTEGRADO CON ASG STUDENT FLOW
            function enrollInCourse() {
                console.log('ASG: enrollInCourse called, currentCourse:', currentCourse);
                // Usar el sistema ASG de inscripción
                if (currentCourse && currentCourse.price) {
                    startEnrollment(ASG_COURSE_CODE, currentCourse.price);
                } else {
                    startEnrollment(ASG_COURSE_CODE, 99); // Precio por defecto
                }
            }

            // Hacer enrollInCourse también global para debugging
            window.enrollInCourse = enrollInCourse;

            console.log('ASG: Funciones definidas - startEnrollment:', typeof startEnrollment, 'enrollInCourse:', typeof enrollInCourse);

            // Verificar estado de inscripción del usuario
            async function checkEnrollmentStatus() {
                try {
                    const response = await fetch(`/wp-json/asg/v1/check-enrollment?course=${COURSE_CODE}`);
                    const result = await response.json();

                    console.log('ASG: Respuesta de check-enrollment:', result);

                    if (result.success) {
                        isEnrolled = result.enrolled; // Corregido: era result.data.enrolled
                        console.log('ASG: Estado de inscripción:', isEnrolled);

                        // Actualizar botones según estado
                        updateActionButtons();

                        return isEnrolled;
                    }
                } catch (error) {
                    console.error('ASG: Error verificando inscripción:', error);
                }

                return false;
            }

            // Actualizar botones de acción según estado de inscripción
            function updateActionButtons() {
                const enrollBtn = document.querySelector('.enroll-btn');
                if (enrollBtn) {
                    if (isEnrolled) {
                        // Usuario inscrito - cambiar a "Continue Learning"
                        enrollBtn.innerHTML = `
                            <i class="bi bi-play-circle me-2"></i>
                            <span>Continue Learning</span>
                        `;
                        enrollBtn.classList.remove('btn-primary');
                        enrollBtn.classList.add('btn-success');
                        enrollBtn.onclick = function() { continueToNextLesson(); };
                    } else {
                        // Usuario no inscrito - mantener "Enroll Now"
                        enrollBtn.innerHTML = `
                            <i class="bi bi-plus-circle me-2"></i>
                            <span>Enroll Now</span>
                        `;
                        enrollBtn.classList.remove('btn-success');
                        enrollBtn.classList.add('btn-primary');
                        enrollBtn.onclick = function() { enrollInCourse(); };
                    }
                }
            }

            // Continuar a la siguiente lección disponible (CORREGIDO)
            function continueToNextLesson() {
                console.log('🎯 Continuando a siguiente lección...');
                
                // Redirigir a la página de lecciones sin lección específica
                // Esto permitirá al usuario elegir desde dónde empezar
                window.location.href = `/lessons/?course=${COURSE_CODE}`;
                
                // Comentar la lógica automática problemática:
                /*
                if (!window.asgCourseProgress) {
                    const firstLesson = findFirstAvailableLesson();
                    if (firstLesson) {
                        window.location.href = `/lessons/?course=${COURSE_CODE}&lesson=${firstLesson}`;
                    }
                    return;
                }
                */
            }

            // Encontrar primera lección disponible
            function findFirstAvailableLesson() {
                if (currentCourse && currentCourse.modules) {
                    for (const module of currentCourse.modules) {
                        if (module.lessons && module.lessons.length > 0) {
                            // Buscar primera lección preview o primera lección en general
                            const previewLesson = module.lessons.find(lesson => parseInt(lesson.is_preview) === 1);
                            if (previewLesson) {
                                return previewLesson.id_lesson;
                            }
                            return module.lessons[0].id_lesson;
                        }
                    }
                }
                return null;
            }

            // Cargar progreso del usuario si está inscrito
            async function loadUserProgressForPreview() {
                if (!isEnrolled) {
                    console.log('ASG: Usuario no inscrito, no se carga progreso');
                    return;
                }

                try {
                    console.log('ASG: Cargando progreso del usuario para vista previa');

                    const response = await fetch(`/wp-json/asg/v1/my-courses`);
                    const result = await response.json();

                    if (result.success && result.data.courses) {
                        // Buscar el curso actual
                        const currentCourseProgress = result.data.courses.find(course =>
                            course.code_course === COURSE_CODE
                        );

                        if (currentCourseProgress) {
                            console.log('ASG: Progreso encontrado:', currentCourseProgress);

                            // Guardar progreso globalmente
                            window.asgCourseProgress = currentCourseProgress;

                            // Actualizar UI con progreso
                            updateLessonListWithProgress(currentCourseProgress);

                            // Mostrar estadísticas de progreso
                            showProgressStats(currentCourseProgress);
                        }
                    }

                } catch (error) {
                    console.error('ASG: Error cargando progreso:', error);
                }
            }

            // Actualizar lista de lecciones con progreso
            function updateLessonListWithProgress(courseProgress) {
                const completedLessons = courseProgress.completed_lessons_list || [];
                const unlockedLessons = courseProgress.unlocked_lessons_list || [];
                const previewLessons = courseProgress.preview_lessons_list || [];

                console.log('ASG: Actualizando UI con progreso:', {
                    completed: completedLessons,
                    unlocked: unlockedLessons,
                    preview: previewLessons
                });

                // Actualizar cada lección en la UI
                document.querySelectorAll('.lesson-item').forEach(lessonElement => {
                    const onclick = lessonElement.getAttribute('onclick');
                    if (onclick) {
                        const lessonIdMatch = onclick.match(/viewLesson\('(\d+)'/);
                        if (lessonIdMatch) {
                            const lessonId = parseInt(lessonIdMatch[1]);

                            // Marcar como completada
                            if (completedLessons.includes(lessonId)) {
                                lessonElement.classList.add('completed');

                                // Agregar ícono de completado
                                const icon = lessonElement.querySelector('.lesson-icon i');
                                if (icon && !icon.classList.contains('bi-check-circle')) {
                                    icon.className = 'bi bi-check-circle text-success';
                                }
                            }

                            // Marcar como bloqueada (solo lecciones no-preview)
                            if (!previewLessons.includes(lessonId) && !unlockedLessons.includes(lessonId)) {
                                lessonElement.classList.add('locked');
                                lessonElement.style.opacity = '0.6';

                                // Agregar ícono de candado
                                const icon = lessonElement.querySelector('.lesson-icon i');
                                if (icon) {
                                    icon.className = 'bi bi-lock text-muted';
                                }
                            }
                        }
                    }
                });
            }

            // Mostrar estadísticas de progreso
            function showProgressStats(courseProgress) {
                const progressPercentage = courseProgress.progress_percentage || 0;
                const completedLessons = courseProgress.completed_lessons || 0;
                const totalLessons = courseProgress.total_lessons || 0;

                // Buscar lugar para mostrar progreso (después del título del curso)
                const courseTitle = document.querySelector('h1, .course-title');
                if (courseTitle && progressPercentage > 0) {
                    const progressHtml = `
                        <div class="course-progress-stats" style="margin: 15px 0; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #28a745;">
                            <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 10px;">
                                <span style="font-weight: 600; color: #333;">Tu Progreso</span>
                                <span style="font-weight: 600; color: #28a745;">${progressPercentage}%</span>
                            </div>
                            <div style="background: #e9ecef; border-radius: 4px; overflow: hidden; height: 8px; margin-bottom: 8px;">
                                <div style="width: ${progressPercentage}%; height: 100%; background: #28a745; transition: width 0.3s ease;"></div>
                            </div>
                            <div style="font-size: 14px; color: #666;">
                                ${completedLessons} de ${totalLessons} lecciones completadas
                            </div>
                        </div>
                    `;

                    // Insertar después del título
                    courseTitle.insertAdjacentHTML('afterend', progressHtml);
                }
            }

            function viewLesson(lessonId, isPreview) {
                console.log('🎯 ViewLesson called:', { lessonId, isPreview, isEnrolled });

                // Check if user can access this lesson
                if (!isPreview && !isEnrolled) {
                    showInfoMessage('Debes inscribirte en el curso para acceder a esta lección.');
                    return;
                }

                // Si está inscrito, verificar sistema secuencial para lecciones no-preview
                if (!isPreview && isEnrolled && window.asgCourseProgress) {
                    const unlockedLessons = window.asgCourseProgress.unlocked_lessons_list || [];
                    const lessonIdInt = parseInt(lessonId, 10);

                    if (!unlockedLessons.includes(lessonIdInt)) {
                        showModal(
                            'Lección bloqueada',
                            '<p>Esta lección está bloqueada.</p>'
                        );
                        return;
                    }
                }

                // Redirect to lessons page
                const lessonUrl = `${window.location.origin}/lessons/?course=${COURSE_CODE}&lesson=${lessonId}`;
                console.log('🔗 Redirecting to:', lessonUrl);

                // Show loading message
                showInfoMessage('Cargando lección...');

                // Redirect after short delay
                setTimeout(() => {
                    window.location.href = lessonUrl;
                }, 500);
            }

            function previewCourse() {
                if (!currentCourse) return;

                // Find first preview lesson
                let firstPreviewLesson = null;
                if (currentCourse.modules) {
                    for (const module of currentCourse.modules) {
                        if (module.lessons) {
                            firstPreviewLesson = module.lessons.find(lesson => parseInt(lesson.is_preview) === 1);
                            if (firstPreviewLesson) break;
                        }
                    }
                }

                if (firstPreviewLesson) {
                    const lessonUrl = `${window.location.origin}/lessons/?course=${COURSE_CODE}&lesson=${firstPreviewLesson.id_lesson}`;
                    showInfoMessage('Opening course preview...');
                    setTimeout(() => {
                        window.location.href = lessonUrl;
                    }, 500);
                } else {
                    showInfoMessage('No preview lessons available for this course.');
                }
            }



            // Funciones de utilidad
            function showLoadingState() {
                document.getElementById('loadingState').style.display = 'flex';
                document.getElementById('courseHero').style.display = 'none';
            }

            function hideLoadingState() {
                document.getElementById('loadingState').style.display = 'none';
                document.getElementById('courseHero').style.display = 'block';
            }

            function showError(message) {
                document.getElementById('loadingState').innerHTML = `
                    <div class="container">
                        <div class="text-center text-white">
                            <i class="bi bi-exclamation-triangle display-1 mb-3"></i>
                            <h4>${message}</h4>
                            <a href="<?php echo $site_url; ?>/student-courses/" class="btn btn-light mt-3">
                                <i class="bi bi-arrow-left me-2"></i>Back to Courses
                            </a>
                        </div>
                    </div>
                `;
            }

            function showSuccessMessage(message) {
                showToast(message, 'success');
            }

            function showInfoMessage(message) {
                showToast(message, 'info');
            }

            function showToast(message, type = 'info') {
                const toast = document.createElement('div');
                toast.className = `toast align-items-center text-white bg-${type === 'success' ? 'success' : 'primary'} border-0`;
                toast.setAttribute('role', 'alert');
                toast.innerHTML = `
                    <div class="d-flex">
                        <div class="toast-body">
                            <i class="bi bi-${type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
                            ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                `;
                
                let toastContainer = document.getElementById('toastContainer');
                if (!toastContainer) {
                    toastContainer = document.createElement('div');
                    toastContainer.id = 'toastContainer';
                    toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
                    toastContainer.style.zIndex = '9999';
                    document.body.appendChild(toastContainer);
                }
                
                toastContainer.appendChild(toast);
                const bsToast = new bootstrap.Toast(toast);
                bsToast.show();
                
                toast.addEventListener('hidden.bs.toast', () => {
                    toast.remove();
                });
            }

            // Efectos de scroll
            function addScrollEffects() {
                const navbar = document.querySelector('.navbar');
                
                if (navbar) {
                    window.addEventListener('scroll', () => {
                        if (window.scrollY > 100) {
                            navbar.style.boxShadow = '0 4px 25px rgba(30, 136, 229, 0.15)';
                        } else {
                            navbar.style.boxShadow = '0 2px 20px rgba(30, 136, 229, 0.08)';
                        }
                    });
                }
            }

            // Animaciones de entrada
            function addEntranceAnimations() {
                const cards = document.querySelectorAll('.enhanced-card');
                cards.forEach((card, index) => {
                    setTimeout(() => {
                        card.classList.add('animate__animated', 'animate__fadeInUp');
                    }, index * 200);
                });
            }
        </script>

        <!-- ASG STUDENT FLOW INTEGRATION -->
        <script>
        // Configuración del curso para ASG Student Flow (solo una declaración)
        window.COURSE_CODE = '<?php echo $course_code; ?>';
        window.COURSE_PRICE = currentCourse ? currentCourse.price : 99;

        // ASG STUDENT FLOW - ENROLLMENT SYSTEM (INLINE)

        // Usar las variables globales (no redeclarar)
   
        const COURSE_PRICE = window.COURSE_PRICE;

        // Función principal de inscripción
        function startEnrollment(courseCode = COURSE_CODE, price = COURSE_PRICE) {
            console.log('ASG: Iniciando proceso de inscripción', { courseCode, price });

            // Verificar si el usuario está logueado
            if (!isUserLoggedIn()) {
                console.log('ASG: Usuario no logueado, mostrando modal');
                showLoginModal(courseCode, price);
                return;
            }

            // Usuario logueado - verificar si ya está inscrito
            checkExistingEnrollment(courseCode, price);
        }

        // Verificar si el usuario está logueado
        function isUserLoggedIn() {
            // Múltiples métodos de verificación para máxima compatibilidad
            return document.body.classList.contains('logged-in') ||
                   document.querySelector('.admin-bar') !== null ||
                   window.wp_user_logged_in === true ||
                   document.querySelector('body.logged-in') !== null;
        }

        // Verificar enrollment existente
        async function checkExistingEnrollment(courseCode, price) {
            try {
                const response = await fetch(`/wp-json/asg/v1/check-enrollment?course=${courseCode}`);
                const result = await response.json();

                if (result.success && result.enrolled) {
                    // Ya está inscrito
                    showAlreadyEnrolledMessage(courseCode);
                } else {
                    // No está inscrito - proceder al pago
                    redirectToPayment(courseCode, price);
                }
            } catch (error) {
                console.error('ASG: Error verificando enrollment:', error);
                // En caso de error, proceder al pago (fail-safe)
                redirectToPayment(courseCode, price);
            }
        }

        // Mostrar modal de login/registro
        function showLoginModal(courseCode, price) {
            // Crear modal si no existe
            let modal = document.getElementById('asg-login-modal');
            if (!modal) {
                modal = createLoginModal();
                document.body.appendChild(modal);
            }

            // Actualizar contenido del modal
            updateModalContent(modal, courseCode, price);

            // Mostrar modal
            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }

        // Crear estructura del modal
        function createLoginModal() {
            const modal = document.createElement('div');
            modal.id = 'asg-login-modal';
            modal.className = 'asg-modal-overlay';

            modal.innerHTML = `
                <div class="asg-modal-content">
                    <button class="asg-modal-close" onclick="closeLoginModal()">&times;</button>
                    <div id="asg-modal-body">
                        <!-- Contenido dinámico -->
                    </div>
                </div>
            `;

            // Event listener para cerrar al hacer clic fuera
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeLoginModal();
                }
            });

            return modal;
        }

        // Actualizar contenido del modal
        function updateModalContent(modal, courseCode, price) {
            const modalBody = modal.querySelector('#asg-modal-body');
            const currentUrl = encodeURIComponent(window.location.href);
            const registerUrl = `/register/?redirect_course=${courseCode}&price=${price}`;
            const loginUrl = `/wp-login.php?redirect_to=${currentUrl}`;

            modalBody.innerHTML = `
                <div class="login-modal-header">
                    <h3>🎓 Para inscribirte necesitas una cuenta</h3>
                    <p>Únete a miles de estudiantes que ya están aprendiendo</p>
                </div>

                <div class="login-options">
                    <a href="${registerUrl}" class="btn btn-primary btn-large">
                        <span class="btn-icon">✨</span>
                        Crear Cuenta Nueva
                        <small>Rápido y gratuito</small>
                    </a>

                    <div class="divider">
                        <span>o</span>
                    </div>

                    <a href="${loginUrl}" class="btn btn-outline-primary btn-large">
                        <span class="btn-icon">🔐</span>
                        Iniciar Sesión
                        <small>Ya tengo cuenta</small>
                    </a>
                </div>

                <div class="benefits-section">
                    <h5>¿Por qué crear una cuenta?</h5>
                    <ul class="benefits-list">
                        <li><span class="benefit-icon">✅</span> Acceso a todos tus cursos</li>
                        <li><span class="benefit-icon">📊</span> Seguimiento de progreso</li>
                        <li><span class="benefit-icon">🏆</span> Certificados descargables</li>
                        <li><span class="benefit-icon">💬</span> Soporte técnico incluido</li>
                    </ul>
                </div>

                <div class="course-reminder">
                    <div class="reminder-content">
                        <h6>📚 Curso seleccionado:</h6>
                        <p><strong>${courseCode}</strong> - $${price} USD</p>
                    </div>
                </div>
            `;
        }

        // Cerrar modal
        function closeLoginModal() {
            const modal = document.getElementById('asg-login-modal');
            if (modal) {
                modal.style.display = 'none';
                document.body.style.overflow = '';
            }
        }

        // Mostrar mensaje de ya inscrito
        function showAlreadyEnrolledMessage(courseCode) {
            // Crear notificación temporal
            const notification = document.createElement('div');
            notification.className = 'asg-notification asg-success';
            notification.innerHTML = `
                <div class="notification-content">
                    <h4>✅ Ya estás inscrito</h4>
                    <p>Ya tienes acceso a este curso.</p>
                    <div class="notification-actions">
                        <a href="/lessons/?course=${courseCode}" class="btn btn-success">Ir a las Lecciones</a>
                        <button onclick="this.parentElement.parentElement.parentElement.remove()" class="btn btn-secondary">Cerrar</button>
                    </div>
                </div>
            `;

            // Insertar al inicio del body
            document.body.insertBefore(notification, document.body.firstChild);

            // Auto-remover después de 10 segundos
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 10000);
        }

        // Redirect al pago
        function redirectToPayment(courseCode, price) {
            const paymentUrl = `/payment/?course=${courseCode}&price=${10}`;
            console.log('ASG: Redirigiendo a pago:', paymentUrl);
            window.location.href = paymentUrl;
        }

        // Función global para ser llamada desde HTML
        window.startEnrollment = startEnrollment;

        console.log('ASG Enrollment System initialized for course:', COURSE_CODE);
        </script>

        <!-- ASG Modal Styles -->
        <style>
        /* ASG Modal Styles */
        .asg-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            backdrop-filter: blur(5px);
        }

        .asg-modal-content {
            background: white;
            border-radius: 15px;
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .asg-modal-close {
            position: absolute;
            top: 15px;
            right: 20px;
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
            z-index: 1;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .asg-modal-close:hover {
            background: #f0f0f0;
            color: #333;
        }

        .login-modal-header {
            text-align: center;
            padding: 30px 30px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
        }

        .login-modal-header h3 {
            margin: 0 0 10px 0;
            font-size: 24px;
        }

        .login-modal-header p {
            margin: 0;
            opacity: 0.9;
            font-size: 16px;
        }

        .login-options {
            padding: 30px;
        }

        .btn {
            display: block;
            width: 100%;
            padding: 15px 20px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            text-align: center;
            cursor: pointer;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-large {
            font-size: 16px;
            margin-bottom: 15px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #667eea;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn-outline-primary {
            background: white;
            color: #667eea;
            border-color: #667eea;
        }

        .btn-outline-primary:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-icon {
            font-size: 18px;
            margin-right: 8px;
        }

        .btn small {
            display: block;
            font-size: 12px;
            opacity: 0.8;
            font-weight: normal;
            margin-top: 2px;
        }

        .divider {
            text-align: center;
            margin: 20px 0;
            position: relative;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e0e0e0;
        }

        .divider span {
            background: white;
            padding: 0 15px;
            color: #666;
            font-size: 14px;
        }

        .benefits-section {
            padding: 0 30px 20px;
        }

        .benefits-section h5 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 16px;
        }

        .benefits-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .benefits-list li {
            padding: 8px 0;
            display: flex;
            align-items: center;
            color: #555;
            font-size: 14px;
        }

        .benefit-icon {
            margin-right: 10px;
            font-size: 16px;
        }

        .course-reminder {
            background: #f8f9fa;
            margin: 0 30px 30px;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .course-reminder h6 {
            margin: 0 0 8px 0;
            color: #333;
            font-size: 14px;
        }

        .course-reminder p {
            margin: 0;
            color: #667eea;
            font-weight: 600;
        }

        /* Notification Styles */
        .asg-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            max-width: 400px;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 9999;
            animation: slideInRight 0.3s ease-out;
        }

        .asg-notification.asg-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .notification-content h4 {
            margin: 0 0 10px 0;
            font-size: 18px;
        }

        .notification-content p {
            margin: 0 0 15px 0;
        }

        .notification-actions {
            display: flex;
            gap: 10px;
        }

        .btn-success {
            background: #28a745;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            text-decoration: none;
            font-size: 14px;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .asg-modal-content {
                width: 95%;
                margin: 20px;
            }

            .login-modal-header {
                padding: 20px 20px 15px;
            }

            .login-options {
                padding: 20px;
            }

            .benefits-section {
                padding: 0 20px 15px;
            }

            .course-reminder {
                margin: 0 20px 20px;
            }

            .asg-notification {
                top: 10px;
                right: 10px;
                left: 10px;
                max-width: none;
            }
        }

        .btn-close-white {
            filter: brightness(0) invert(1);
        }

        </style>

        <!-- Modal genérico -->
        <div class="modal fade" id="genericModal" tabindex="-1" aria-labelledby="genericModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered"> <!-- centrado verticalmente -->
                <div class="modal-content" style="border-radius: var(--border-radius);">
                
                    <!-- Cabecera en gradiente primario -->
                    <div class="modal-header" style="
                            background: var(--primary-gradient);
                            color: var(--white);
                            border-bottom: none;
                        ">
                        <h5 class="modal-title" id="genericModalLabel">Título</h5>
                        <!-- botón cierre en blanco -->
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Cerrar"></button>
                    </div>
                    
                    <!-- Cuerpo blanco -->
                    <div class="modal-body" id="genericModalBody" style="background: var(--white); color: var(--text-dark);">
                    </div>
                    
                    <!-- Pie con botón primario -->
                    <div class="modal-footer" style="border-top: none; background: var(--white);">
                        <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Cerrar</button>
                    </div>
                </div>
            </div>
        </div>


    </body>
    </html>
<?php
}

/**
 * Registrar la página de detalle de curso mejorada
 */
function asg_register_enhanced_course_detail_page() {
    $page_slug = 'course-detail';
    $page = get_page_by_path($page_slug);
    
    if (!$page) {
        $page_data = array(
            'post_title'    => 'Enhanced Course Detail',
            'post_content'  => '[asg_course_detail]',
            'post_status'   => 'publish',
            'post_type'     => 'page',
            'post_name'     => $page_slug
        );
        wp_insert_post($page_data);
    }
}

// Reemplazar la función del shortcode:
function asg_course_detail_shortcode() {
    ob_start();
    asg_enhanced_course_detail_page();
    return ob_get_clean();
}

add_shortcode('asg_course_detail', 'asg_course_detail_shortcode');
add_action('init', 'asg_register_enhanced_course_detail_page');
